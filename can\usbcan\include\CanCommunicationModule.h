#ifndef CAN_COMMUNICATION_MODULE_H
#define CAN_COMMUNICATION_MODULE_H

#include <memory>
#include <optional>
#include <chrono>
#include <functional>
#include <ros/ros.h>
#include <usbcan/UsbCan.h>
#include <usbcan/can.h>

/**
 * @brief CAN通信模块
 * 负责CAN设备的初始化、消息收发、设备状态检查等功能
 */
class CanCommunicationModule
{
public:
    /**
     * @brief 构造函数
     */
    CanCommunicationModule();

    /**
     * @brief 析构函数
     */
    ~CanCommunicationModule();

    /**
     * @brief 初始化CAN通信模块
     * @return true 初始化成功，false 初始化失败
     */
    bool initialize();

    /**
     * @brief 接收CAN消息
     * @param channel CAN通道号
     * @return 接收到的CAN消息，如果没有消息则返回空
     */
    std::optional<VCI_CAN_OBJ> receiveMessage(int channel = 0);

    /**
     * @brief 发送CAN消息
     * @param message 要发送的CAN消息
     * @param channel CAN通道号
     * @return true 发送成功，false 发送失败
     */
    bool sendMessage(const VCI_CAN_OBJ& message, int channel = 0);

    /**
     * @brief 检查CAN设备状态
     * @return true 设备正常，false 设备异常
     */
    bool checkDeviceStatus();

    /**
     * @brief 获取最后一次接收消息的时间
     * @return 最后接收消息的时间点
     */
    std::chrono::steady_clock::time_point getLastReceiveTime() const;

    /**
     * @brief 检查是否有消息超时
     * @param timeout_seconds 超时时间（秒）
     * @return true 有超时，false 无超时
     */
    bool isMessageTimeout(int timeout_seconds = 5) const;

    /**
     * @brief 清空接收缓冲区
     * @param channel CAN通道号
     * @return true 清空成功，false 清空失败
     */
    bool clearBuffer(int channel = 0);

    /**
     * @brief 获取设备连接状态
     * @return true 已连接，false 未连接
     */
    bool isDeviceConnected() const;

private:
    std::unique_ptr<UsbCan> usb_can_;                                    // CAN设备对象
    std::chrono::steady_clock::time_point last_receive_time_;            // 最后接收消息时间
    std::chrono::steady_clock::time_point last_status_check_time_;       // 最后状态检查时间
    bool device_connected_;                                              // 设备连接状态
    int no_message_count_;                                               // 无消息计数器

    /**
     * @brief 更新最后接收消息时间
     */
    void updateLastReceiveTime();

    /**
     * @brief 记录通信错误
     * @param error_msg 错误消息
     */
    void logCommunicationError(const std::string& error_msg);
};

#endif // CAN_COMMUNICATION_MODULE_H
