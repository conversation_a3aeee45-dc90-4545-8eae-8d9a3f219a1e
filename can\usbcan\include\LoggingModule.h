#ifndef LOGGING_MODULE_H
#define LOGGING_MODULE_H

#include <string>
#include <vector>
#include <fstream>
#include <mutex>
#include <chrono>
#include <ros/ros.h>
#include <usbcan/can.h>

/**
 * @brief 日志配置结构体
 */
struct LoggingConfig
{
    int log_rotation_interval_sec;       // 日志文件切换间隔(秒)
    int log_retention_days;              // 日志文件保留天数
    bool enable_logging;                 // 是否启用CAN数据记录
    bool enable_real_time_flush;         // 是否启用实时刷新
    int max_log_file_size_mb;            // 单个日志文件最大大小(MB)
    std::string log_file_prefix;         // 日志文件名前缀
    std::vector<uint32_t> can_ids_to_log;    // 需要记录的CAN ID列表
    std::vector<uint32_t> can_ids_to_filter; // 需要过滤的CAN ID列表
    
    LoggingConfig() {
        // 默认配置
        log_rotation_interval_sec = 60;
        log_retention_days = 7;
        enable_logging = true;
        enable_real_time_flush = true;
        max_log_file_size_mb = 100;
        log_file_prefix = "can_data";
    }
};

/**
 * @brief 日志记录模块
 * 负责CAN数据的日志记录、文件管理、配置加载等功能
 */
class LoggingModule
{
public:
    /**
     * @brief 构造函数
     */
    LoggingModule();

    /**
     * @brief 析构函数
     */
    ~LoggingModule();

    /**
     * @brief 初始化日志模块
     * @param config_file_path 配置文件路径（可选）
     * @return true 初始化成功，false 初始化失败
     */
    bool initialize(const std::string& config_file_path = "");

    /**
     * @brief 记录CAN数据到文件
     * @param can_obj CAN消息对象
     * @return true 记录成功，false 记录失败
     */
    bool logCanData(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 设置日志配置
     * @param config 日志配置
     */
    void setLoggingConfig(const LoggingConfig& config);

    /**
     * @brief 获取当前日志配置
     * @return 当前日志配置
     */
    const LoggingConfig& getLoggingConfig() const;

    /**
     * @brief 手动切换日志文件
     * @return true 切换成功，false 切换失败
     */
    bool rotateLogFile();

    /**
     * @brief 清理旧日志文件
     * @return 删除的文件数量
     */
    int cleanupOldLogFiles();

    /**
     * @brief 获取当前日志文件大小
     * @return 文件大小（字节）
     */
    size_t getCurrentLogFileSize() const;

    /**
     * @brief 获取日志目录路径
     * @return 日志目录路径
     */
    std::string getLogDirectory() const;

    /**
     * @brief 获取当前日志文件名
     * @return 当前日志文件名
     */
    std::string getCurrentLogFileName() const;

    /**
     * @brief 检查是否应该记录指定的CAN ID
     * @param can_id CAN消息ID
     * @return true 应该记录，false 不应该记录
     */
    bool shouldLogCanId(uint32_t can_id) const;

    /**
     * @brief 强制刷新当前日志文件
     */
    void flushCurrentLogFile();

    /**
     * @brief 关闭当前日志文件
     */
    void closeCurrentLogFile();

    /**
     * @brief 获取日志统计信息
     * @return 统计信息字符串
     */
    std::string getLoggingStatistics() const;

private:
    LoggingConfig config_;                           // 日志配置
    std::string log_directory_;                      // 日志目录
    std::ofstream current_log_file_;                 // 当前日志文件流
    std::string current_log_filename_;               // 当前日志文件名
    std::mutex log_mutex_;                           // 日志文件互斥锁
    
    // 统计信息
    uint64_t total_messages_logged_;                 // 总记录消息数
    std::chrono::system_clock::time_point start_time_; // 模块启动时间
    std::chrono::system_clock::time_point last_rotation_time_; // 上次切换时间

    /**
     * @brief 加载配置文件
     * @param config_file_path 配置文件路径
     * @return true 加载成功，false 加载失败
     */
    bool loadConfigFromFile(const std::string& config_file_path);

    /**
     * @brief 创建日志目录
     * @return true 创建成功，false 创建失败
     */
    bool createLogDirectory();

    /**
     * @brief 创建新的日志文件
     * @return true 创建成功，false 创建失败
     */
    bool createNewLogFile();

    /**
     * @brief 生成日志文件名
     * @return 日志文件名
     */
    std::string generateLogFileName() const;

    /**
     * @brief 检查并根据大小切换日志文件
     * @return true 文件已切换，false 文件未切换
     */
    bool checkAndRotateLogFileBySize();

    /**
     * @brief 格式化CAN数据为字符串
     * @param can_obj CAN消息对象
     * @return 格式化后的字符串
     */
    std::string formatCanData(const VCI_CAN_OBJ& can_obj) const;

    /**
     * @brief 获取当前时间戳字符串
     * @return 时间戳字符串
     */
    std::string getCurrentTimestamp() const;

    /**
     * @brief 记录错误信息
     * @param error_msg 错误消息
     */
    void logError(const std::string& error_msg);
};

#endif // LOGGING_MODULE_H
