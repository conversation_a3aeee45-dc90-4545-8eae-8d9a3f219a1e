#ifndef MESSAGE_PROCESSOR_MODULE_H
#define MESSAGE_PROCESSOR_MODULE_H

#include <functional>
#include <unordered_map>
#include <chrono>
#include <ros/ros.h>
#include <usbcan/can.h>
#include <usbcan/can_msg.h>
#include "djyxlogger.h"

/**
 * @brief 消息处理结果结构体
 */
struct MessageProcessResult
{
    bool success;                    // 处理是否成功
    std::string error_message;       // 错误信息
    std::string message_type;        // 消息类型
    
    MessageProcessResult(bool s = true, const std::string& err = "", const std::string& type = "")
        : success(s), error_message(err), message_type(type) {}
};

/**
 * @brief 按钮消息数据结构
 */
struct ButtonMessageData
{
    int button_value;
    int previous_button;
    bool is_valid_button;
    std::string button_name;
};

/**
 * @brief 速度消息数据结构
 */
struct SpeedMessageData
{
    double wheel_speed;
    double left_speed;
    double right_speed;
    int left_encoder;
    int right_encoder;
};

/**
 * @brief BMS消息数据结构
 */
struct BmsMessageData
{
    uint8_t soc_value;
    bool is_battery_low;
};

/**
 * @brief 车辆控制模式消息数据结构
 */
struct VehicleControlModeData
{
    int control_mode;
    bool is_auto_mode;
};

/**
 * @brief 消息处理模块
 * 负责各种CAN消息的解析和处理逻辑
 */
class MessageProcessorModule
{
public:
    // 消息处理回调函数类型定义
    using ButtonMessageCallback = std::function<void(const ButtonMessageData&)>;
    using SpeedMessageCallback = std::function<void(const SpeedMessageData&)>;
    using BmsMessageCallback = std::function<void(const BmsMessageData&)>;
    using VehicleControlModeCallback = std::function<void(const VehicleControlModeData&)>;
    using BodyFaultMessageCallback = std::function<void(const VCI_CAN_OBJ&)>;

    /**
     * @brief 构造函数
     * @param logger 日志记录器引用
     */
    explicit MessageProcessorModule(djyx::Logger& logger);

    /**
     * @brief 析构函数
     */
    ~MessageProcessorModule() = default;

    /**
     * @brief 处理CAN消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 设置按钮消息处理回调
     * @param callback 回调函数
     */
    void setButtonMessageCallback(const ButtonMessageCallback& callback);

    /**
     * @brief 设置按钮消息2处理回调
     * @param callback 回调函数
     */
    void setButtonMessage2Callback(const ButtonMessageCallback& callback);

    /**
     * @brief 设置速度消息处理回调
     * @param callback 回调函数
     */
    void setSpeedMessageCallback(const SpeedMessageCallback& callback);

    /**
     * @brief 设置BMS消息处理回调
     * @param callback 回调函数
     */
    void setBmsMessageCallback(const BmsMessageCallback& callback);

    /**
     * @brief 设置车辆控制模式消息处理回调
     * @param callback 回调函数
     */
    void setVehicleControlModeCallback(const VehicleControlModeCallback& callback);

    /**
     * @brief 设置车身故障消息处理回调
     * @param callback 回调函数
     */
    void setBodyFaultMessageCallback(const BodyFaultMessageCallback& callback);

    /**
     * @brief 获取消息处理统计信息
     * @return 统计信息字符串
     */
    std::string getProcessingStatistics() const;

private:
    djyx::Logger& logger_;                                               // 日志记录器引用
    
    // 消息处理回调函数
    ButtonMessageCallback button_callback_;
    ButtonMessageCallback button2_callback_;
    SpeedMessageCallback speed_callback_;
    BmsMessageCallback bms_callback_;
    VehicleControlModeCallback vehicle_control_mode_callback_;
    BodyFaultMessageCallback body_fault_callback_;

    // 消息处理统计
    std::unordered_map<uint32_t, uint64_t> message_count_;               // 各类消息计数
    std::chrono::steady_clock::time_point start_time_;                   // 模块启动时间

    // 调试输出控制
    std::chrono::steady_clock::time_point last_debug_time_;
    int debug_counter_;

    /**
     * @brief 处理按钮消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processButtonMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 处理按钮消息2
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processButtonMessage2(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 处理速度消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processSpeedMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 处理BMS消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processBmsMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 处理车辆控制模式消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processVehicleControlModeMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 处理车身故障消息
     * @param can_obj CAN消息对象
     * @return 处理结果
     */
    MessageProcessResult processBodyFaultMessage(const VCI_CAN_OBJ& can_obj);

    /**
     * @brief 更新消息统计
     * @param message_id 消息ID
     */
    void updateMessageStatistics(uint32_t message_id);

    /**
     * @brief 检查是否应该输出调试信息
     * @return true 应该输出，false 不应该输出
     */
    bool shouldPrintDebug();
};

#endif // MESSAGE_PROCESSOR_MODULE_H
