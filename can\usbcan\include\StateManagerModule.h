#ifndef STATE_MANAGER_MODULE_H
#define STATE_MANAGER_MODULE_H

#include <ros/ros.h>
#include <std_msgs/Bool.h>
#include <std_msgs/String.h>
#include <std_msgs/Int32.h>
#include <auto_msgs/TopicState.h>
#include <geometry_msgs/PoseStamped.h>
#include <chrono>
#include <string>
#include <functional>
#include "djyxlogger.h"

/**
 * @brief 车辆状态标志集合
 */
struct VehicleStateFlags
{
    // 基础状态标志
    bool mission_stop;           // 任务暂停
    bool is_block;               // 路径被阻挡
    bool safety_triggered;       // 安全框触发
    bool is_too_far;             // 偏离路径过远
    bool topic_stop;             // 话题停止
    bool located_flag;           // 定位状态（true=异常，false=正常）
    bool obstacle_detected;      // 障碍物检测
    
    // 车辆操作状态
    bool hooking;                // 正在挂钩
    bool is_stopped;             // 已停车
    bool has_stopped;            // 曾经停车
    bool arrived;                // 已到达目标
    bool associate_arrived;      // 已到达关联目标
    bool have_associate;         // 有关联目标
    
    // 控制状态
    bool control_mode;           // 控制模式
    bool auto_hook;              // 自动挂钩
    bool voice_flag;             // 语音标志
    bool error_elimination;      // 错误消除
    
    // 电池和硬件状态
    bool is_battery_low;         // 电池低电量
    bool hookup_flag;            // 挂钩完成标志
    
    // 倒车相关状态
    bool current_goal_reverse_flag;      // 当前目标倒车标志
    std::string current_goal_reverse_direction; // 倒车方向
    
    // 定时器状态
    bool red_light_flag;         // 红灯警告标志
    bool time_count;             // 红灯计时标志
    bool time_count_hook;        // 挂钩计时标志
    
    // 话题健康状态
    bool accept_messages;        // 接受消息标志
    std::string block_topic;     // 被阻塞的话题名称
    
    VehicleStateFlags() {
        // 初始化所有标志为默认值
        mission_stop = false;
        is_block = false;
        safety_triggered = false;
        is_too_far = false;
        topic_stop = false;
        located_flag = false;
        obstacle_detected = false;
        
        hooking = false;
        is_stopped = false;
        has_stopped = false;
        arrived = false;
        associate_arrived = false;
        have_associate = false;
        
        control_mode = false;
        auto_hook = false;
        voice_flag = true;
        error_elimination = true;
        
        is_battery_low = false;
        hookup_flag = false;
        
        current_goal_reverse_flag = false;
        current_goal_reverse_direction = "left";
        
        red_light_flag = false;
        time_count = false;
        time_count_hook = false;
        
        accept_messages = false;
        block_topic = "";
    }
};

/**
 * @brief 定时器状态结构体
 */
struct TimerState
{
    ros::Time start_time;        // 开始时间
    ros::Time start_time_hook;   // 挂钩开始时间
    ros::Time stop_time_start;   // 停车开始时间
    bool is_timing;              // 是否正在计时
    
    TimerState() : is_timing(false) {}
};

/**
 * @brief 状态管理模块
 * 负责车辆各种状态标志的管理和状态转换
 */
class StateManagerModule
{
public:
    // 状态变化回调函数类型
    using StateChangeCallback = std::function<void(const std::string&, bool, bool)>; // 状态名，旧值，新值

    /**
     * @brief 构造函数
     * @param logger 日志记录器引用
     */
    explicit StateManagerModule(djyx::Logger& logger);

    /**
     * @brief 析构函数
     */
    ~StateManagerModule() = default;

    /**
     * @brief 获取当前状态标志
     * @return 状态标志集合
     */
    const VehicleStateFlags& getStateFlags() const;

    /**
     * @brief 设置状态变化回调
     * @param callback 回调函数
     */
    void setStateChangeCallback(const StateChangeCallback& callback);

    // 状态设置方法
    void setMissionStop(bool value);
    void setIsBlock(bool value);
    void setSafetyTriggered(bool value);
    void setIsTooFar(bool value);
    void setTopicStop(bool value);
    void setLocatedFlag(bool value);
    void setObstacleDetected(bool value);
    void setHooking(bool value);
    void setIsStopped(bool value);
    void setHasStopped(bool value);
    void setArrived(bool value);
    void setAssociateArrived(bool value);
    void setHaveAssociate(bool value);
    void setControlMode(bool value);
    void setAutoHook(bool value);
    void setVoiceFlag(bool value);
    void setErrorElimination(bool value);
    void setIsBatteryLow(bool value);
    void setHookupFlag(bool value);
    void setCurrentGoalReverseFlag(bool value);
    void setCurrentGoalReverseDirection(const std::string& direction);
    void setRedLightFlag(bool value);
    void setTimeCount(bool value);
    void setTimeCountHook(bool value);
    void setAcceptMessages(bool value);
    void setBlockTopic(const std::string& topic);

    /**
     * @brief 处理定时器事件
     * @param wheel_speed 当前车轮速度
     */
    void handleTimerEvent(double wheel_speed);

    /**
     * @brief 处理话题状态回调
     * @param msg 话题状态消息
     */
    void handleTopicStateCallback(const auto_msgs::TopicState::ConstPtr& msg);

    /**
     * @brief 获取定时器状态
     * @return 定时器状态
     */
    const TimerState& getTimerState() const;

    /**
     * @brief 重置所有状态为默认值
     */
    void resetAllStates();

    /**
     * @brief 获取状态摘要字符串
     * @return 状态摘要
     */
    std::string getStateSummary() const;

    /**
     * @brief 检查是否需要停车
     * @return true 需要停车，false 不需要停车
     */
    bool shouldVehicleStop() const;

private:
    djyx::Logger& logger_;                           // 日志记录器引用
    VehicleStateFlags state_flags_;                  // 状态标志集合
    TimerState timer_state_;                         // 定时器状态
    StateChangeCallback state_change_callback_;      // 状态变化回调
    ros::Time last_received_;                        // 最后接收消息时间

    // 定时器相关常量
    static constexpr double SPEED_THRESHOLD = 0.05;      // 车速阈值 (m/s)
    static constexpr double RED_LIGHT_TIMEOUT = 30.0;    // 红灯警告超时时间 (s)
    static constexpr double HOOKUP_TIMEOUT = 5.0;        // 挂钩超时时间 (s)
    static constexpr double MAX_TIMER_DURATION = 3600.0; // 最大计时时长 (s)

    /**
     * @brief 通知状态变化
     * @param state_name 状态名称
     * @param old_value 旧值
     * @param new_value 新值
     */
    void notifyStateChange(const std::string& state_name, bool old_value, bool new_value);

    /**
     * @brief 处理红灯警告计时器
     * @param wheel_speed 车轮速度
     */
    void handleRedLightTimer(double wheel_speed);

    /**
     * @brief 处理挂钩计时器
     * @param wheel_speed 车轮速度
     */
    void handleHookupTimer(double wheel_speed);
};

#endif // STATE_MANAGER_MODULE_H
