#ifndef UTILITY_MODULE_H
#define UTILITY_MODULE_H

#include <string>
#include <vector>
#include <chrono>
#include <cmath>
#include <fstream>
#include <ros/ros.h>
#include <geometry_msgs/PoseStamped.h>
#include <geometry_msgs/Point.h>

/**
 * @brief 数学工具类
 */
class MathUtils
{
public:
    /**
     * @brief 计算两点之间的欧几里得距离
     * @param p1 第一个点
     * @param p2 第二个点
     * @return 距离值
     */
    static double calculateDistance(const geometry_msgs::Point& p1, const geometry_msgs::Point& p2);

    /**
     * @brief 计算两个角度之间的差值（考虑角度环绕）
     * @param angle1 第一个角度（弧度）
     * @param angle2 第二个角度（弧度）
     * @return 角度差值（弧度）
     */
    static double angleDifference(double angle1, double angle2);

    /**
     * @brief 将角度标准化到 [-π, π] 范围
     * @param angle 输入角度（弧度）
     * @return 标准化后的角度
     */
    static double normalizeAngle(double angle);

    /**
     * @brief 将角度从弧度转换为度
     * @param radians 弧度值
     * @return 度数值
     */
    static double radiansToDegrees(double radians);

    /**
     * @brief 将角度从度转换为弧度
     * @param degrees 度数值
     * @return 弧度值
     */
    static double degreesToRadians(double degrees);

    /**
     * @brief 限制数值在指定范围内
     * @param value 输入值
     * @param min_val 最小值
     * @param max_val 最大值
     * @return 限制后的值
     */
    template<typename T>
    static T clamp(T value, T min_val, T max_val) {
        return std::max(min_val, std::min(value, max_val));
    }

    /**
     * @brief 线性插值
     * @param a 起始值
     * @param b 结束值
     * @param t 插值参数 [0, 1]
     * @return 插值结果
     */
    static double lerp(double a, double b, double t);

    /**
     * @brief 检查浮点数是否近似相等
     * @param a 第一个数
     * @param b 第二个数
     * @param epsilon 误差阈值
     * @return true 如果近似相等
     */
    static bool isApproximatelyEqual(double a, double b, double epsilon = 1e-9);
};

/**
 * @brief 时间工具类
 */
class TimeUtils
{
public:
    /**
     * @brief 获取当前时间戳（毫秒）
     * @return 时间戳
     */
    static uint64_t getCurrentTimestampMs();

    /**
     * @brief 获取当前时间戳（微秒）
     * @return 时间戳
     */
    static uint64_t getCurrentTimestampUs();

    /**
     * @brief 获取格式化的当前时间字符串
     * @param format 时间格式字符串
     * @return 格式化的时间字符串
     */
    static std::string getCurrentTimeString(const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief 计算两个时间点之间的差值（秒）
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（秒）
     */
    static double getTimeDifferenceSeconds(const ros::Time& start, const ros::Time& end);

    /**
     * @brief 检查是否超时
     * @param start_time 开始时间
     * @param timeout_seconds 超时时间（秒）
     * @return true 如果超时
     */
    static bool isTimeout(const ros::Time& start_time, double timeout_seconds);

    /**
     * @brief 休眠指定毫秒数
     * @param milliseconds 毫秒数
     */
    static void sleepMs(int milliseconds);
};

/**
 * @brief 文件工具类
 */
class FileUtils
{
public:
    /**
     * @brief 检查文件是否存在
     * @param file_path 文件路径
     * @return true 如果文件存在
     */
    static bool fileExists(const std::string& file_path);

    /**
     * @brief 检查目录是否存在
     * @param dir_path 目录路径
     * @return true 如果目录存在
     */
    static bool directoryExists(const std::string& dir_path);

    /**
     * @brief 创建目录（递归创建）
     * @param dir_path 目录路径
     * @return true 如果创建成功
     */
    static bool createDirectory(const std::string& dir_path);

    /**
     * @brief 获取文件大小
     * @param file_path 文件路径
     * @return 文件大小（字节），失败返回-1
     */
    static long getFileSize(const std::string& file_path);

    /**
     * @brief 读取文件内容到字符串
     * @param file_path 文件路径
     * @return 文件内容，失败返回空字符串
     */
    static std::string readFileToString(const std::string& file_path);

    /**
     * @brief 将字符串写入文件
     * @param file_path 文件路径
     * @param content 文件内容
     * @param append 是否追加模式
     * @return true 如果写入成功
     */
    static bool writeStringToFile(const std::string& file_path, const std::string& content, bool append = false);

    /**
     * @brief 获取文件扩展名
     * @param file_path 文件路径
     * @return 文件扩展名（不包含点）
     */
    static std::string getFileExtension(const std::string& file_path);

    /**
     * @brief 获取文件名（不包含路径）
     * @param file_path 文件路径
     * @return 文件名
     */
    static std::string getFileName(const std::string& file_path);

    /**
     * @brief 获取目录路径（不包含文件名）
     * @param file_path 文件路径
     * @return 目录路径
     */
    static std::string getDirectoryPath(const std::string& file_path);
};

/**
 * @brief 字符串工具类
 */
class StringUtils
{
public:
    /**
     * @brief 去除字符串两端的空白字符
     * @param str 输入字符串
     * @return 处理后的字符串
     */
    static std::string trim(const std::string& str);

    /**
     * @brief 将字符串转换为小写
     * @param str 输入字符串
     * @return 小写字符串
     */
    static std::string toLowerCase(const std::string& str);

    /**
     * @brief 将字符串转换为大写
     * @param str 输入字符串
     * @return 大写字符串
     */
    static std::string toUpperCase(const std::string& str);

    /**
     * @brief 分割字符串
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @return 分割后的字符串向量
     */
    static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

    /**
     * @brief 连接字符串向量
     * @param strings 字符串向量
     * @param delimiter 连接符
     * @return 连接后的字符串
     */
    static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief 检查字符串是否以指定前缀开始
     * @param str 输入字符串
     * @param prefix 前缀
     * @return true 如果以前缀开始
     */
    static bool startsWith(const std::string& str, const std::string& prefix);

    /**
     * @brief 检查字符串是否以指定后缀结束
     * @param str 输入字符串
     * @param suffix 后缀
     * @return true 如果以后缀结束
     */
    static bool endsWith(const std::string& str, const std::string& suffix);

    /**
     * @brief 替换字符串中的所有匹配项
     * @param str 输入字符串
     * @param from 要替换的子字符串
     * @param to 替换为的字符串
     * @return 替换后的字符串
     */
    static std::string replaceAll(const std::string& str, const std::string& from, const std::string& to);
};

/**
 * @brief 车辆相关的工具函数
 */
class VehicleUtils
{
public:
    /**
     * @brief 速度单位转换：m/s 转 km/h
     * @param mps 米每秒
     * @return 千米每小时
     */
    static double mpsToKmh(double mps);

    /**
     * @brief 速度单位转换：km/h 转 m/s
     * @param kmh 千米每小时
     * @return 米每秒
     */
    static double kmhToMps(double kmh);

    /**
     * @brief 计算车轮转速对应的线速度
     * @param wheel_rpm 车轮转速（转/分钟）
     * @param wheel_diameter_mm 车轮直径（毫米）
     * @return 线速度（m/s）
     */
    static double wheelRpmToLinearVelocity(double wheel_rpm, double wheel_diameter_mm);

    /**
     * @brief 计算线速度对应的车轮转速
     * @param linear_velocity 线速度（m/s）
     * @param wheel_diameter_mm 车轮直径（毫米）
     * @return 车轮转速（转/分钟）
     */
    static double linearVelocityToWheelRpm(double linear_velocity, double wheel_diameter_mm);

    /**
     * @brief 计算阿克曼转向几何的内外轮转角
     * @param front_wheel_angle 前轮转角（弧度）
     * @param wheelbase 轴距（米）
     * @param track_width 轮距（米）
     * @param inner_angle 内轮转角（输出）
     * @param outer_angle 外轮转角（输出）
     */
    static void calculateAckermannAngles(double front_wheel_angle, double wheelbase, double track_width,
                                       double& inner_angle, double& outer_angle);
};

#endif // UTILITY_MODULE_H
