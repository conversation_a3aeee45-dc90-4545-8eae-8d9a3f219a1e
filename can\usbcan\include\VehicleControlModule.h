#ifndef VEHICLE_CONTROL_MODULE_H
#define VEHICLE_CONTROL_MODULE_H

#include <ros/ros.h>
#include <auto_msgs/ControlCommandStamped.h>
#include <geometry_msgs/PoseStamped.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float32.h>
#include <algorithm>
#include <cmath>
#include "djyxlogger.h"

/**
 * @brief 车辆控制参数结构体
 */
struct VehicleControlParams
{
    double forward_speed;        // 前进速度
    double retreat_speed;        // 后退速度
    double left_angle;           // 左转角度
    double right_angle;          // 右转角度
    char gear;                   // 档位
    bool is_braking;             // 是否刹车
    
    VehicleControlParams()
        : forward_speed(0.0), retreat_speed(0.0), left_angle(0.0), right_angle(0.0)
        , gear(0x22), is_braking(false) {}
};

/**
 * @brief 车辆状态结构体
 */
struct VehicleState
{
    double wheel_speed;          // 车轮速度
    bool is_block;               // 路径是否被阻挡
    bool is_located;             // 定位是否正常
    bool is_too_far;             // 是否偏离路径过远
    bool is_safety_triggered;    // 安全框是否触发
    bool is_obstacle_detected;   // 是否检测到障碍物
    bool is_mission_stopped;     // 任务是否暂停
    bool is_topic_stopped;       // 话题是否停止
    bool is_hooking;             // 是否正在挂钩
    bool is_stopped;             // 是否已停车
    
    VehicleState()
        : wheel_speed(0.0), is_block(false), is_located(true), is_too_far(false)
        , is_safety_triggered(false), is_obstacle_detected(false), is_mission_stopped(false)
        , is_topic_stopped(false), is_hooking(false), is_stopped(false) {}
};

/**
 * @brief PID控制器参数
 */
struct PIDParams
{
    float kp;                    // 比例系数
    float ki;                    // 积分系数
    float kd;                    // 微分系数
    float integral;              // 积分项
    float previous_error;        // 上一次误差
    
    PIDParams(float p = 10.0f, float i = 0.1f, float d = 0.01f)
        : kp(p), ki(i), kd(d), integral(0.0f), previous_error(0.0f) {}
        
    void reset() {
        integral = 0.0f;
        previous_error = 0.0f;
    }
};

/**
 * @brief 车辆控制模块
 * 负责车辆的速度控制、转向控制、档位控制等功能
 */
class VehicleControlModule
{
public:
    /**
     * @brief 构造函数
     * @param logger 日志记录器引用
     */
    explicit VehicleControlModule(djyx::Logger& logger);

    /**
     * @brief 析构函数
     */
    ~VehicleControlModule() = default;

    /**
     * @brief 处理控制命令
     * @param msg 控制命令消息
     * @param vehicle_state 当前车辆状态
     * @return 车辆控制参数
     */
    VehicleControlParams processControlCommand(
        const auto_msgs::ControlCommandStampedConstPtr& msg,
        const VehicleState& vehicle_state);

    /**
     * @brief 更新车辆状态
     * @param state 新的车辆状态
     */
    void updateVehicleState(const VehicleState& state);

    /**
     * @brief 获取当前控制参数
     * @return 当前控制参数
     */
    VehicleControlParams getCurrentControlParams() const;

    /**
     * @brief 重置PID控制器
     */
    void resetPIDController();

    /**
     * @brief 设置PID参数
     * @param params PID参数
     */
    void setPIDParams(const PIDParams& params);

    /**
     * @brief 获取PID参数
     * @return 当前PID参数
     */
    PIDParams getPIDParams() const;

    /**
     * @brief 强制停车
     * @param reason 停车原因
     */
    void forceStop(const std::string& reason);

    /**
     * @brief 检查是否需要停车
     * @param vehicle_state 车辆状态
     * @return true 需要停车，false 不需要停车
     */
    bool shouldStop(const VehicleState& vehicle_state) const;

    /**
     * @brief 获取停车原因
     * @param vehicle_state 车辆状态
     * @return 停车原因字符串
     */
    std::string getStopReason(const VehicleState& vehicle_state) const;

    /**
     * @brief 计算刹车力度
     * @param current_speed 当前速度
     * @param target_speed 目标速度（通常为0）
     * @return 刹车力度
     */
    double calculateBrakeForce(double current_speed, double target_speed = 0.0);

private:
    djyx::Logger& logger_;                           // 日志记录器引用
    VehicleControlParams current_params_;            // 当前控制参数
    VehicleState current_state_;                     // 当前车辆状态
    PIDParams pid_params_;                           // PID控制器参数
    
    float last_velocity_;                            // 上一次的速度值
    double last_stop_log_time_;                      // 上次停车日志时间
    
    /**
     * @brief 计算转向角度
     * @param steering_angle 方向盘角度（弧度）
     * @return 转向角度值
     */
    double calculateSteeringAngle(double steering_angle);

    /**
     * @brief 处理刹车逻辑
     * @param velocity 目标速度
     * @param vehicle_state 车辆状态
     * @return 控制参数
     */
    VehicleControlParams processBrakeLogic(float velocity, const VehicleState& vehicle_state);

    /**
     * @brief 处理前进逻辑
     * @param velocity 目标速度
     * @return 控制参数
     */
    VehicleControlParams processForwardLogic(float velocity);

    /**
     * @brief 处理后退逻辑
     * @param velocity 目标速度
     * @return 控制参数
     */
    VehicleControlParams processReverseLogic(float velocity);

    /**
     * @brief 记录停车日志
     * @param reason 停车原因
     */
    void logStopReason(const std::string& reason);
};

#endif // VEHICLE_CONTROL_MODULE_H
