#ifndef VEHICLE_CONTROLLER_H
#define VEHICLE_CONTROLLER_H

// 标准库头文件
#include <vector>
#include <memory>
#include <string>
#include <map>

// ROS头文件
#include <ros/ros.h>
#include <std_msgs/Float32.h>
#include <std_msgs/Bool.h>
#include <std_msgs/UInt8MultiArray.h>
#include <std_msgs/UInt8.h>
#include <geometry_msgs/PoseStamped.h>
#include <auto_msgs/ControlCommandStamped.h>
#include <auto_msgs/MissionState.h>
#include <auto_msgs/TopicState.h>
#include <usbcan_msgs/WheelsEncoder.h>
#include <usbcan_msgs/gps.h>
#include <usbcan_msgs/steering_angle.h>

// 项目头文件
#include "djyxlogger.h"
#include "ReverseAdjustmentModule.h"
#include "CanCommunicationModule.h"
#include "MessageProcessorModule.h"
#include "VehicleControlModule.h"
#include "StateManagerModule.h"
#include "LoggingModule.h"
#include "UtilityModule.h"

/**
 * @brief 目标点结构体
 */
struct Goal
{
    std::string name;
    double position_x;
    double position_y;
    double position_z;
    double orientation_x;
    double orientation_y;
    double orientation_z;
    double orientation_w;
    bool reverse_flag;
    std::string reverse_direction;
};

/**
 * @brief 重构后的车辆控制器类
 * 作为各个功能模块的协调者，负责模块间的通信和整体控制逻辑
 */
class VehicleController
{
public:
    /**
     * @brief 构造函数
     * @param nh ROS节点句柄
     * @param logger 日志记录器
     */
    VehicleController(ros::NodeHandle& nh, djyx::Logger& logger);

    /**
     * @brief 析构函数
     */
    ~VehicleController();

    /**
     * @brief 初始化控制器
     * @return true 初始化成功，false 初始化失败
     */
    bool initialize();

    /**
     * @brief 启动控制器
     */
    void start();

    /**
     * @brief 停止控制器
     */
    void stop();

    /**
     * @brief 处理循环
     */
    void processLoop();

private:
    // ROS相关
    ros::NodeHandle& nh_;
    djyx::Logger& logger_;
    ros::Timer timer_;

    // 功能模块
    std::unique_ptr<CanCommunicationModule> can_comm_module_;
    std::unique_ptr<MessageProcessorModule> message_processor_;
    std::unique_ptr<VehicleControlModule> vehicle_control_;
    std::unique_ptr<StateManagerModule> state_manager_;
    std::unique_ptr<LoggingModule> logging_module_;
    ReverseAdjustmentModule reverse_module_;

    // ROS发布者
    ros::Publisher wheels_pub_;
    ros::Publisher speed_pub_;
    ros::Publisher goal_pub_;
    ros::Publisher soc_pub_;
    ros::Publisher error_info_pub_;
    ros::Publisher reverse_status_pub_;
    ros::Publisher steering_angle_pub_;

    // ROS订阅者
    ros::Subscriber ctrl_cmd_sub_;
    ros::Subscriber mission_state_sub_;
    ros::Subscriber topic_state_sub_;
    ros::Subscriber current_pose_sub_;

    // 目标点管理
    std::vector<Goal> goals_;
    std::map<std::string, Goal> goal_map_;

    // 车辆状态数据
    double wheel_speed_;
    geometry_msgs::PoseStamped current_pose_;
    std_msgs::UInt8MultiArray error_info_;

    // 控制参数
    double forward_speed_;
    double retreat_speed_;
    double left_angle_;
    double right_angle_;

    /**
     * @brief 初始化ROS发布者和订阅者
     */
    void initializeRosInterface();

    /**
     * @brief 初始化功能模块
     * @return true 初始化成功，false 初始化失败
     */
    bool initializeModules();

    /**
     * @brief 设置模块间的回调函数
     */
    void setupModuleCallbacks();

    /**
     * @brief 读取目标点JSON文件
     * @return 目标点列表
     */
    std::vector<Goal> readJsonFile();

    /**
     * @brief 定时器事件处理
     * @param event 定时器事件
     */
    void eventTimer(const ros::TimerEvent& event);

    /**
     * @brief 发送CAN控制消息
     */
    void sendCanControlMessage();

    // ROS回调函数
    void ctrlCmdCallback(const auto_msgs::ControlCommandStampedConstPtr& msg);
    void missionStateCallback(const auto_msgs::MissionState::ConstPtr& msg);
    void topicStateCallback(const auto_msgs::TopicState::ConstPtr& msg);
    void currentPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg);
    void isBlockCallback(const std_msgs::Bool::ConstPtr& msg);
    void isTooFarCallback(const std_msgs::Bool::ConstPtr& msg);
    void safetyBoxCallback(const std_msgs::Bool::ConstPtr& msg);
    void obstacleWaypointCallback(const std_msgs::Bool::ConstPtr& msg);
    void forceReverseCallback(const std_msgs::Bool::ConstPtr& msg);

    // 消息处理回调函数
    void onButtonMessage(const ButtonMessageData& data);
    void onButtonMessage2(const ButtonMessageData& data);
    void onSpeedMessage(const SpeedMessageData& data);
    void onBmsMessage(const BmsMessageData& data);
    void onVehicleControlModeMessage(const VehicleControlModeData& data);
    void onBodyFaultMessage(const VCI_CAN_OBJ& can_obj);

    // 状态变化回调函数
    void onStateChange(const std::string& state_name, bool old_value, bool new_value);

    /**
     * @brief 发布车辆状态信息
     */
    void publishVehicleStatus();

    /**
     * @brief 处理CAN消息
     * @param can_obj CAN消息对象
     */
    void handleCanMessage(const VCI_CAN_OBJ& can_obj);
};

#endif // VEHICLE_CONTROLLER_H