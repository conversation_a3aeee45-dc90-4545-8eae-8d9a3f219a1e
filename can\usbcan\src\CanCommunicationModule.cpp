#include "CanCommunicationModule.h"
#include <iostream>
#include <stdexcept>

CanCommunicationModule::CanCommunicationModule()
    : usb_can_(nullptr)
    , last_receive_time_(std::chrono::steady_clock::now())
    , last_status_check_time_(std::chrono::steady_clock::now())
    , device_connected_(false)
    , no_message_count_(0)
{
}

CanCommunicationModule::~CanCommunicationModule()
{
    // 析构时自动清理资源
    if (usb_can_) {
        usb_can_.reset();
    }
}

bool CanCommunicationModule::initialize()
{
    try {
        // 创建UsbCan对象
        usb_can_ = std::make_unique<UsbCan>();
        device_connected_ = true;
        
        printf("\n🚀 CAN Communication Module initialized successfully\n");
        fflush(stdout);
        ROS_INFO("CAN Communication Module initialized successfully");
        
        return true;
    }
    catch (const std::exception& e) {
        device_connected_ = false;
        logCommunicationError("Failed to initialize CAN device: " + std::string(e.what()));
        return false;
    }
}

std::optional<VCI_CAN_OBJ> CanCommunicationModule::receiveMessage(int channel)
{
    if (!usb_can_ || !device_connected_) {
        return std::nullopt;
    }

    try {
        auto message = usb_can_->receive(channel);
        if (message) {
            updateLastReceiveTime();
            no_message_count_ = 0;
        } else {
            no_message_count_++;
        }
        return message;
    }
    catch (const std::exception& e) {
        logCommunicationError("Failed to receive CAN message: " + std::string(e.what()));
        return std::nullopt;
    }
}

bool CanCommunicationModule::sendMessage(const VCI_CAN_OBJ& message, int channel)
{
    if (!usb_can_ || !device_connected_) {
        return false;
    }

    try {
        usb_can_->send(message, channel);
        return true;
    }
    catch (const std::exception& e) {
        logCommunicationError("Failed to send CAN message: " + std::string(e.what()));
        return false;
    }
}

bool CanCommunicationModule::checkDeviceStatus()
{
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_status_check_time_);

    // 每10秒检查一次设备状态
    if (elapsed.count() >= 10) {
        printf("\n🔍 CAN Device Status Check\n");
        fflush(stdout);
        ROS_INFO("Performing CAN device status check");
        last_status_check_time_ = current_time;

        // 这里可以添加具体的设备检查逻辑
        // 目前返回连接状态
        return device_connected_;
    }

    return device_connected_;
}

std::chrono::steady_clock::time_point CanCommunicationModule::getLastReceiveTime() const
{
    return last_receive_time_;
}

bool CanCommunicationModule::isMessageTimeout(int timeout_seconds) const
{
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_receive_time_);
    
    bool is_timeout = elapsed.count() >= timeout_seconds;
    
    // 如果超时且计数器达到一定值时报告问题
    if (is_timeout && no_message_count_ % 1000 == 0) {
        printf("\n⚠️  CAN TIMEOUT! No messages for %ld seconds (count: %d)\n", 
               elapsed.count(), no_message_count_);
        fflush(stdout);
        ROS_WARN("CAN communication timeout! No messages received for %ld seconds", elapsed.count());
    }
    
    return is_timeout;
}

bool CanCommunicationModule::clearBuffer(int channel)
{
    if (!usb_can_ || !device_connected_) {
        return false;
    }

    try {
        // 这里可以添加清空缓冲区的具体实现
        // 目前返回true表示成功
        return true;
    }
    catch (const std::exception& e) {
        logCommunicationError("Failed to clear CAN buffer: " + std::string(e.what()));
        return false;
    }
}

bool CanCommunicationModule::isDeviceConnected() const
{
    return device_connected_;
}

void CanCommunicationModule::updateLastReceiveTime()
{
    last_receive_time_ = std::chrono::steady_clock::now();
}

void CanCommunicationModule::logCommunicationError(const std::string& error_msg)
{
    printf("\n❌ CAN COMMUNICATION ERROR: %s\n", error_msg.c_str());
    fflush(stdout);
    ROS_ERROR("CAN Communication Error: %s", error_msg.c_str());
}
