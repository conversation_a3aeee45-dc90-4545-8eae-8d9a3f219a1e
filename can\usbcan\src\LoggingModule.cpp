#include "LoggingModule.h"
#include <filesystem>
#include <yaml-cpp/yaml.h>
#include <ros/package.h>
#include <iostream>
#include <iomanip>
#include <sstream>
#include <algorithm>

LoggingModule::LoggingModule()
    : total_messages_logged_(0)
    , start_time_(std::chrono::system_clock::now())
    , last_rotation_time_(std::chrono::system_clock::now())
{
}

LoggingModule::~LoggingModule()
{
    closeCurrentLogFile();
}

bool LoggingModule::initialize(const std::string& config_file_path)
{
    try {
        // 加载配置
        if (!config_file_path.empty()) {
            if (!loadConfigFromFile(config_file_path)) {
                ROS_WARN("Failed to load config from file, using default config");
            }
        }

        // 创建日志目录
        if (!createLogDirectory()) {
            logError("Failed to create log directory");
            return false;
        }

        // 启动时先清理一次旧文件
        cleanupOldLogFiles();

        // 创建初始日志文件
        if (!createNewLogFile()) {
            logError("Failed to create initial log file");
            return false;
        }

        ROS_INFO("Logging Module initialized successfully");
        ROS_INFO("  - Logging enabled: %s", config_.enable_logging ? "true" : "false");
        ROS_INFO("  - Log directory: %s", log_directory_.c_str());
        ROS_INFO("  - Log retention: %d days", config_.log_retention_days);
        ROS_INFO("  - Max file size: %d MB", config_.max_log_file_size_mb);

        return true;
    }
    catch (const std::exception& e) {
        logError("Failed to initialize logging module: " + std::string(e.what()));
        return false;
    }
}

bool LoggingModule::logCanData(const VCI_CAN_OBJ& can_obj)
{
    if (!config_.enable_logging || !shouldLogCanId(can_obj.ID)) {
        return true; // 不需要记录，但不算错误
    }

    std::lock_guard<std::mutex> lock(log_mutex_);

    if (!current_log_file_.is_open()) {
        return false;
    }

    try {
        // 检查文件大小，如果超过限制则切换到新文件
        if (checkAndRotateLogFileBySize()) {
            if (!current_log_file_.is_open()) {
                return false;
            }
        }

        // 格式化并写入CAN数据
        std::string formatted_data = formatCanData(can_obj);
        current_log_file_ << "[" << getCurrentTimestamp() << "] " << formatted_data << std::endl;

        // 根据配置决定是否立即刷新到磁盘
        if (config_.enable_real_time_flush) {
            current_log_file_.flush();
        }

        total_messages_logged_++;
        return true;
    }
    catch (const std::exception& e) {
        logError("Failed to log CAN data: " + std::string(e.what()));
        return false;
    }
}

void LoggingModule::setLoggingConfig(const LoggingConfig& config)
{
    config_ = config;
}

const LoggingConfig& LoggingModule::getLoggingConfig() const
{
    return config_;
}

bool LoggingModule::rotateLogFile()
{
    std::lock_guard<std::mutex> lock(log_mutex_);
    
    // 关闭当前文件
    if (current_log_file_.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        current_log_file_ << "\n# Log file rotated: "
                          << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
        current_log_file_.close();
    }

    // 创建新文件
    bool success = createNewLogFile();
    if (success) {
        last_rotation_time_ = std::chrono::system_clock::now();
        ROS_INFO("Log file rotated successfully");
    }
    
    return success;
}

int LoggingModule::cleanupOldLogFiles()
{
    try {
        auto now = std::chrono::system_clock::now();
        auto cutoff_time = now - std::chrono::hours(24 * config_.log_retention_days);

        int deleted_count = 0;

        // 遍历日志目录中的所有文件
        for (const auto& entry : std::filesystem::directory_iterator(log_directory_)) {
            if (entry.is_regular_file()) {
                auto file_time = std::filesystem::last_write_time(entry);

                // 转换文件时间为system_clock时间点
                auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    file_time - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());

                // 如果文件超过保留期限，删除它
                if (sctp < cutoff_time) {
                    std::string filename = entry.path().filename().string();

                    // 确保不删除当前正在使用的日志文件
                    if (filename != current_log_filename_) {
                        std::filesystem::remove(entry.path());
                        deleted_count++;
                        ROS_INFO("Deleted old log file: %s", filename.c_str());
                    }
                }
            }
        }

        if (deleted_count > 0) {
            ROS_INFO("Log cleanup completed. Deleted %d old files.", deleted_count);
        }

        return deleted_count;
    }
    catch (const std::exception& e) {
        logError("Failed to cleanup old log files: " + std::string(e.what()));
        return -1;
    }
}

size_t LoggingModule::getCurrentLogFileSize() const
{
    if (!current_log_file_.is_open() || current_log_filename_.empty()) {
        return 0;
    }

    try {
        std::string full_path = log_directory_ + "/" + current_log_filename_;
        if (std::filesystem::exists(full_path)) {
            return std::filesystem::file_size(full_path);
        }
    }
    catch (const std::exception& e) {
        ROS_WARN("Failed to get log file size: %s", e.what());
    }

    return 0;
}

std::string LoggingModule::getLogDirectory() const
{
    return log_directory_;
}

std::string LoggingModule::getCurrentLogFileName() const
{
    return current_log_filename_;
}

bool LoggingModule::shouldLogCanId(uint32_t can_id) const
{
    // 如果在过滤列表中，不记录
    if (!config_.can_ids_to_filter.empty()) {
        if (std::find(config_.can_ids_to_filter.begin(), config_.can_ids_to_filter.end(), can_id) 
            != config_.can_ids_to_filter.end()) {
            return false;
        }
    }

    // 如果指定了要记录的ID列表，且不为空，则只记录列表中的ID
    if (!config_.can_ids_to_log.empty()) {
        return std::find(config_.can_ids_to_log.begin(), config_.can_ids_to_log.end(), can_id) 
               != config_.can_ids_to_log.end();
    }

    // 默认记录所有ID
    return true;
}

void LoggingModule::flushCurrentLogFile()
{
    std::lock_guard<std::mutex> lock(log_mutex_);
    if (current_log_file_.is_open()) {
        current_log_file_.flush();
    }
}

void LoggingModule::closeCurrentLogFile()
{
    std::lock_guard<std::mutex> lock(log_mutex_);

    if (current_log_file_.is_open()) {
        // 写入文件结束标记
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        current_log_file_ << "\n# Log file closed: "
                          << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
        current_log_file_.close();
        ROS_INFO("Closed log file: %s", current_log_filename_.c_str());
    }
}

std::string LoggingModule::getLoggingStatistics() const
{
    std::stringstream ss;
    auto current_time = std::chrono::system_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time_);
    
    ss << "Logging Statistics:\n";
    ss << "  Running time: " << elapsed.count() << " seconds\n";
    ss << "  Total messages logged: " << total_messages_logged_ << "\n";
    ss << "  Current file size: " << getCurrentLogFileSize() << " bytes\n";
    ss << "  Log directory: " << log_directory_ << "\n";
    ss << "  Current file: " << current_log_filename_ << "\n";
    
    return ss.str();
}

bool LoggingModule::loadConfigFromFile(const std::string& config_file_path)
{
    try {
        std::string config_file = config_file_path;
        
        // 如果没有提供完整路径，尝试从包路径构建
        if (config_file.empty()) {
            std::string package_path = ros::package::getPath("usbcan");
            if (!package_path.empty()) {
                config_file = package_path + "/config/can_logging.yaml";
            }
        }

        // 检查配置文件是否存在
        if (!std::filesystem::exists(config_file)) {
            ROS_WARN("Config file not found: %s", config_file.c_str());
            return false;
        }

        // 加载YAML配置文件
        YAML::Node config = YAML::LoadFile(config_file);

        // 读取配置参数
        config_.log_rotation_interval_sec = config["log_rotation_interval"].as<int>(60);
        config_.log_retention_days = config["log_retention_days"].as<int>(7);
        config_.enable_logging = config["enable_logging"].as<bool>(true);
        config_.enable_real_time_flush = config["enable_real_time_flush"].as<bool>(true);
        config_.max_log_file_size_mb = config["max_log_file_size_mb"].as<int>(100);
        config_.log_file_prefix = config["log_file_prefix"].as<std::string>("can_data");

        // 读取CAN ID列表
        if (config["can_ids_to_log"] && config["can_ids_to_log"].IsSequence()) {
            config_.can_ids_to_log.clear();
            for (const auto& id : config["can_ids_to_log"]) {
                config_.can_ids_to_log.push_back(id.as<uint32_t>());
            }
        }

        if (config["can_ids_to_filter"] && config["can_ids_to_filter"].IsSequence()) {
            config_.can_ids_to_filter.clear();
            for (const auto& id : config["can_ids_to_filter"]) {
                config_.can_ids_to_filter.push_back(id.as<uint32_t>());
            }
        }

        ROS_INFO("Logging configuration loaded from: %s", config_file.c_str());
        return true;
    }
    catch (const std::exception& e) {
        logError("Failed to load config file: " + std::string(e.what()));
        return false;
    }
}

bool LoggingModule::createLogDirectory()
{
    try {
        // 获取包路径
        std::string package_path = ros::package::getPath("usbcan");
        if (package_path.empty()) {
            log_directory_ = "/tmp/can_logs";
        } else {
            log_directory_ = package_path + "/logs";
        }

        // 创建目录
        if (!std::filesystem::exists(log_directory_)) {
            std::filesystem::create_directories(log_directory_);
            ROS_INFO("Created log directory: %s", log_directory_.c_str());
        }

        return true;
    }
    catch (const std::exception& e) {
        logError("Failed to create log directory: " + std::string(e.what()));
        return false;
    }
}

bool LoggingModule::createNewLogFile()
{
    try {
        current_log_filename_ = generateLogFileName();
        std::string full_path = log_directory_ + "/" + current_log_filename_;

        current_log_file_.open(full_path, std::ios::out | std::ios::app);
        if (!current_log_file_.is_open()) {
            logError("Failed to open log file: " + full_path);
            return false;
        }

        // 写入文件头
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        current_log_file_ << "# CAN Data Log File\n";
        current_log_file_ << "# Created: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << "\n";
        current_log_file_ << "# Format: [timestamp] ID:data_length:data_bytes\n\n";
        current_log_file_.flush();

        ROS_INFO("Created new log file: %s", current_log_filename_.c_str());
        return true;
    }
    catch (const std::exception& e) {
        logError("Failed to create new log file: " + std::string(e.what()));
        return false;
    }
}

std::string LoggingModule::generateLogFileName() const
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << config_.log_file_prefix << "_"
       << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
       << ".log";

    return ss.str();
}

bool LoggingModule::checkAndRotateLogFileBySize()
{
    size_t current_size = getCurrentLogFileSize();
    size_t max_size = static_cast<size_t>(config_.max_log_file_size_mb) * 1024 * 1024;

    if (current_size >= max_size) {
        ROS_INFO("Log file size (%zu bytes) exceeded limit (%zu bytes), rotating...",
                 current_size, max_size);

        // 关闭当前文件
        if (current_log_file_.is_open()) {
            current_log_file_.close();
        }

        // 创建新文件
        return createNewLogFile();
    }

    return false; // 没有切换文件
}

std::string LoggingModule::formatCanData(const VCI_CAN_OBJ& can_obj) const
{
    std::stringstream ss;

    // 格式: ID:data_length:data_bytes
    ss << std::hex << std::uppercase << "0x" << can_obj.ID << ":"
       << std::dec << static_cast<int>(can_obj.DataLen) << ":";

    for (int i = 0; i < can_obj.DataLen; ++i) {
        if (i > 0) ss << " ";
        ss << std::hex << std::uppercase << std::setfill('0') << std::setw(2)
           << static_cast<int>(can_obj.Data[i]);
    }

    return ss.str();
}

std::string LoggingModule::getCurrentTimestamp() const
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}

void LoggingModule::logError(const std::string& error_msg)
{
    ROS_ERROR("[LoggingModule] %s", error_msg.c_str());
}
