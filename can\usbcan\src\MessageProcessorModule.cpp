#include "MessageProcessorModule.h"
#include <iostream>
#include <iomanip>
#include <sstream>

MessageProcessorModule::MessageProcessorModule(djyx::Logger& logger)
    : logger_(logger)
    , start_time_(std::chrono::steady_clock::now())
    , last_debug_time_(std::chrono::steady_clock::now())
    , debug_counter_(0)
{
    ROS_INFO("Message Processor Module initialized");
}

MessageProcessResult MessageProcessorModule::processMessage(const VCI_CAN_OBJ& can_obj)
{
    updateMessageStatistics(can_obj.ID);

    try {
        // 根据CAN消息ID分发到不同的处理函数
        switch (can_obj.ID)
        {
        case 0x110:
            return processBodyFaultMessage(can_obj);
        case 0x301:
            return processButtonMessage(can_obj);
        case 0x302:
            return processButtonMessage2(can_obj);
        case 0x283:
            return processVehicleControlModeMessage(can_obj);
        case 0x288:
            return processBmsMessage(can_obj);
        case 0x287:
            return processSpeedMessage(can_obj);
        default:
            // 未知消息ID，记录但不处理
            if (shouldPrintDebug()) {
                printf("🔍 Unknown CAN message ID: 0x%X\n", can_obj.ID);
                fflush(stdout);
            }
            return MessageProcessResult(true, "", "unknown");
        }
    }
    catch (const std::exception& e) {
        std::string error_msg = "Exception in message processing: " + std::string(e.what());
        ROS_ERROR("%s", error_msg.c_str());
        return MessageProcessResult(false, error_msg, "exception");
    }
}

void MessageProcessorModule::setButtonMessageCallback(const ButtonMessageCallback& callback)
{
    button_callback_ = callback;
}

void MessageProcessorModule::setButtonMessage2Callback(const ButtonMessageCallback& callback)
{
    button2_callback_ = callback;
}

void MessageProcessorModule::setSpeedMessageCallback(const SpeedMessageCallback& callback)
{
    speed_callback_ = callback;
}

void MessageProcessorModule::setBmsMessageCallback(const BmsMessageCallback& callback)
{
    bms_callback_ = callback;
}

void MessageProcessorModule::setVehicleControlModeCallback(const VehicleControlModeCallback& callback)
{
    vehicle_control_mode_callback_ = callback;
}

void MessageProcessorModule::setBodyFaultMessageCallback(const BodyFaultMessageCallback& callback)
{
    body_fault_callback_ = callback;
}

MessageProcessResult MessageProcessorModule::processButtonMessage(const VCI_CAN_OBJ& can_obj)
{
    try {
        can_msg::button button_data(can_obj.Data);
        int button = button_data.button_data();

        ButtonMessageData data;
        data.button_value = button;
        data.is_valid_button = (button != 0 && button < 17);
        data.button_name = "button" + std::to_string(button);

        if (button_callback_) {
            button_callback_(data);
        }

        return MessageProcessResult(true, "", "button");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process button message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "button");
    }
}

MessageProcessResult MessageProcessorModule::processButtonMessage2(const VCI_CAN_OBJ& can_obj)
{
    try {
        can_msg::button button_data(can_obj.Data);
        int button = button_data.button_data();

        ButtonMessageData data;
        data.button_value = button;
        data.is_valid_button = (button != 0 && button < 3);
        data.button_name = "button" + std::to_string(button);

        if (button2_callback_) {
            button2_callback_(data);
        }

        return MessageProcessResult(true, "", "button2");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process button2 message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "button2");
    }
}

MessageProcessResult MessageProcessorModule::processSpeedMessage(const VCI_CAN_OBJ& can_obj)
{
    try {
        can_msg::DJ2_speed velo(can_obj.Data);
        
        SpeedMessageData data;
        double ratio = 12.7;
        int wheel_diameter = 500;
        data.wheel_speed = velo.speed() / 60 * 3.1415926 / ratio * wheel_diameter / 1000 * 3.6;
        data.left_speed = data.wheel_speed;
        data.right_speed = data.wheel_speed;
        
        // 计算编码器值（这里需要VelocityToPulse类的实现）
        data.left_encoder = 0;  // 临时值
        data.right_encoder = 0; // 临时值

        if (speed_callback_) {
            speed_callback_(data);
        }

        return MessageProcessResult(true, "", "speed");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process speed message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "speed");
    }
}

MessageProcessResult MessageProcessorModule::processBmsMessage(const VCI_CAN_OBJ& can_obj)
{
    try {
        can_msg::DJ2_BMS bms(can_obj.Data);
        
        BmsMessageData data;
        data.soc_value = bms.YCB_SOC();
        data.is_battery_low = data.soc_value <= 10;

        if (data.is_battery_low) {
            logger_.logError("Battery Low!");
        }

        if (bms_callback_) {
            bms_callback_(data);
        }

        return MessageProcessResult(true, "", "bms");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process BMS message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "bms");
    }
}

MessageProcessResult MessageProcessorModule::processVehicleControlModeMessage(const VCI_CAN_OBJ& can_obj)
{
    try {
        can_msg::vechicle_control data_parser(can_obj.Data);
        
        VehicleControlModeData data;
        data.control_mode = data_parser.control_mode();
        data.is_auto_mode = (data.control_mode == 1); // 假设1表示自动模式

        if (vehicle_control_mode_callback_) {
            vehicle_control_mode_callback_(data);
        }

        return MessageProcessResult(true, "", "vehicle_control_mode");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process vehicle control mode message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "vehicle_control_mode");
    }
}

MessageProcessResult MessageProcessorModule::processBodyFaultMessage(const VCI_CAN_OBJ& can_obj)
{
    try {
        if (body_fault_callback_) {
            body_fault_callback_(can_obj);
        }

        return MessageProcessResult(true, "", "body_fault");
    }
    catch (const std::exception& e) {
        std::string error_msg = "Failed to process body fault message: " + std::string(e.what());
        return MessageProcessResult(false, error_msg, "body_fault");
    }
}

void MessageProcessorModule::updateMessageStatistics(uint32_t message_id)
{
    message_count_[message_id]++;
}

bool MessageProcessorModule::shouldPrintDebug()
{
    debug_counter_++;
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_debug_time_);

    // 每3秒输出一次调试信息
    if (elapsed.count() >= 3) {
        last_debug_time_ = current_time;
        return true;
    }
    return false;
}

std::string MessageProcessorModule::getProcessingStatistics() const
{
    std::stringstream ss;
    ss << "Message Processing Statistics:\n";
    
    auto current_time = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time_);
    ss << "Running time: " << elapsed.count() << " seconds\n";
    
    for (const auto& pair : message_count_) {
        ss << "ID 0x" << std::hex << pair.first << ": " << std::dec << pair.second << " messages\n";
    }
    
    return ss.str();
}
