#include "StateManagerModule.h"
#include <iostream>
#include <sstream>

StateManagerModule::StateManagerModule(djyx::Logger& logger)
    : logger_(logger)
    , last_received_(ros::Time::now())
{
    ROS_INFO("State Manager Module initialized");
}

const VehicleStateFlags& StateManagerModule::getStateFlags() const
{
    return state_flags_;
}

void StateManagerModule::setStateChangeCallback(const StateChangeCallback& callback)
{
    state_change_callback_ = callback;
}

void StateManagerModule::setMissionStop(bool value)
{
    bool old_value = state_flags_.mission_stop;
    state_flags_.mission_stop = value;
    notifyStateChange("mission_stop", old_value, value);
}

void StateManagerModule::setIsBlock(bool value)
{
    bool old_value = state_flags_.is_block;
    state_flags_.is_block = value;
    notifyStateChange("is_block", old_value, value);
}

void StateManagerModule::setSafetyTriggered(bool value)
{
    bool old_value = state_flags_.safety_triggered;
    state_flags_.safety_triggered = value;
    notifyStateChange("safety_triggered", old_value, value);
}

void StateManagerModule::setIsTooFar(bool value)
{
    bool old_value = state_flags_.is_too_far;
    state_flags_.is_too_far = value;
    notifyStateChange("is_too_far", old_value, value);
}

void StateManagerModule::setTopicStop(bool value)
{
    bool old_value = state_flags_.topic_stop;
    state_flags_.topic_stop = value;
    notifyStateChange("topic_stop", old_value, value);
}

void StateManagerModule::setLocatedFlag(bool value)
{
    bool old_value = state_flags_.located_flag;
    state_flags_.located_flag = value;
    notifyStateChange("located_flag", old_value, value);
}

void StateManagerModule::setObstacleDetected(bool value)
{
    bool old_value = state_flags_.obstacle_detected;
    state_flags_.obstacle_detected = value;
    notifyStateChange("obstacle_detected", old_value, value);
}

void StateManagerModule::setHooking(bool value)
{
    bool old_value = state_flags_.hooking;
    state_flags_.hooking = value;
    notifyStateChange("hooking", old_value, value);
}

void StateManagerModule::setIsStopped(bool value)
{
    bool old_value = state_flags_.is_stopped;
    state_flags_.is_stopped = value;
    notifyStateChange("is_stopped", old_value, value);
}

void StateManagerModule::setHasStopped(bool value)
{
    bool old_value = state_flags_.has_stopped;
    state_flags_.has_stopped = value;
    notifyStateChange("has_stopped", old_value, value);
}

void StateManagerModule::setArrived(bool value)
{
    bool old_value = state_flags_.arrived;
    state_flags_.arrived = value;
    notifyStateChange("arrived", old_value, value);
}

void StateManagerModule::setAssociateArrived(bool value)
{
    bool old_value = state_flags_.associate_arrived;
    state_flags_.associate_arrived = value;
    notifyStateChange("associate_arrived", old_value, value);
}

void StateManagerModule::setHaveAssociate(bool value)
{
    bool old_value = state_flags_.have_associate;
    state_flags_.have_associate = value;
    notifyStateChange("have_associate", old_value, value);
}

void StateManagerModule::setControlMode(bool value)
{
    bool old_value = state_flags_.control_mode;
    state_flags_.control_mode = value;
    notifyStateChange("control_mode", old_value, value);
}

void StateManagerModule::setAutoHook(bool value)
{
    bool old_value = state_flags_.auto_hook;
    state_flags_.auto_hook = value;
    notifyStateChange("auto_hook", old_value, value);
}

void StateManagerModule::setVoiceFlag(bool value)
{
    bool old_value = state_flags_.voice_flag;
    state_flags_.voice_flag = value;
    notifyStateChange("voice_flag", old_value, value);
}

void StateManagerModule::setErrorElimination(bool value)
{
    bool old_value = state_flags_.error_elimination;
    state_flags_.error_elimination = value;
    notifyStateChange("error_elimination", old_value, value);
}

void StateManagerModule::setIsBatteryLow(bool value)
{
    bool old_value = state_flags_.is_battery_low;
    state_flags_.is_battery_low = value;
    notifyStateChange("is_battery_low", old_value, value);
}

void StateManagerModule::setHookupFlag(bool value)
{
    bool old_value = state_flags_.hookup_flag;
    state_flags_.hookup_flag = value;
    notifyStateChange("hookup_flag", old_value, value);
}

void StateManagerModule::setCurrentGoalReverseFlag(bool value)
{
    bool old_value = state_flags_.current_goal_reverse_flag;
    state_flags_.current_goal_reverse_flag = value;
    notifyStateChange("current_goal_reverse_flag", old_value, value);
}

void StateManagerModule::setCurrentGoalReverseDirection(const std::string& direction)
{
    std::string old_direction = state_flags_.current_goal_reverse_direction;
    state_flags_.current_goal_reverse_direction = direction;
    
    if (state_change_callback_) {
        // 对于字符串类型，我们使用特殊的处理方式
        ROS_INFO("State changed: current_goal_reverse_direction from '%s' to '%s'", 
                 old_direction.c_str(), direction.c_str());
    }
}

void StateManagerModule::setRedLightFlag(bool value)
{
    bool old_value = state_flags_.red_light_flag;
    state_flags_.red_light_flag = value;
    notifyStateChange("red_light_flag", old_value, value);
}

void StateManagerModule::setTimeCount(bool value)
{
    bool old_value = state_flags_.time_count;
    state_flags_.time_count = value;
    notifyStateChange("time_count", old_value, value);
}

void StateManagerModule::setTimeCountHook(bool value)
{
    bool old_value = state_flags_.time_count_hook;
    state_flags_.time_count_hook = value;
    notifyStateChange("time_count_hook", old_value, value);
}

void StateManagerModule::setAcceptMessages(bool value)
{
    bool old_value = state_flags_.accept_messages;
    state_flags_.accept_messages = value;
    notifyStateChange("accept_messages", old_value, value);
}

void StateManagerModule::setBlockTopic(const std::string& topic)
{
    std::string old_topic = state_flags_.block_topic;
    state_flags_.block_topic = topic;
    
    if (state_change_callback_) {
        ROS_INFO("State changed: block_topic from '%s' to '%s'", 
                 old_topic.c_str(), topic.c_str());
    }
}

void StateManagerModule::handleTimerEvent(double wheel_speed)
{
    ros::Time now = ros::Time::now();
    
    // 处理红灯警告计时器
    handleRedLightTimer(wheel_speed);
    
    // 处理自动挂钩计时器
    handleHookupTimer(wheel_speed);
}

void StateManagerModule::handleTopicStateCallback(const auto_msgs::TopicState::ConstPtr& msg)
{
    last_received_ = ros::Time::now();
    
    bool topic_flag = msg->topic_state;
    std::string topic = msg->topic;
    
    if (!topic_flag && state_flags_.accept_messages) {
        logger_.logError(topic + " Error!");
        setTopicStop(true);
        setAcceptMessages(false);
        setBlockTopic(topic);
    }
    else if (topic == state_flags_.block_topic) {
        if (topic_flag) {
            setAcceptMessages(true);
            setTopicStop(false);
            setBlockTopic("");
        }
    }
}

const TimerState& StateManagerModule::getTimerState() const
{
    return timer_state_;
}

void StateManagerModule::resetAllStates()
{
    state_flags_ = VehicleStateFlags(); // 重置为默认值
    timer_state_ = TimerState();
    ROS_INFO("All states reset to default values");
}

std::string StateManagerModule::getStateSummary() const
{
    std::stringstream ss;
    ss << "Vehicle State Summary:\n";
    ss << "  Mission Stop: " << (state_flags_.mission_stop ? "YES" : "NO") << "\n";
    ss << "  Is Block: " << (state_flags_.is_block ? "YES" : "NO") << "\n";
    ss << "  Safety Triggered: " << (state_flags_.safety_triggered ? "YES" : "NO") << "\n";
    ss << "  Located Flag: " << (state_flags_.located_flag ? "ABNORMAL" : "NORMAL") << "\n";
    ss << "  Control Mode: " << (state_flags_.control_mode ? "AUTO" : "MANUAL") << "\n";
    ss << "  Battery Low: " << (state_flags_.is_battery_low ? "YES" : "NO") << "\n";
    ss << "  Arrived: " << (state_flags_.arrived ? "YES" : "NO") << "\n";
    ss << "  Hooking: " << (state_flags_.hooking ? "YES" : "NO") << "\n";
    
    return ss.str();
}

bool StateManagerModule::shouldVehicleStop() const
{
    return state_flags_.is_block || 
           state_flags_.located_flag || 
           state_flags_.hooking || 
           state_flags_.is_stopped || 
           state_flags_.mission_stop || 
           state_flags_.is_too_far || 
           state_flags_.safety_triggered || 
           state_flags_.obstacle_detected ||
           state_flags_.topic_stop;
}

void StateManagerModule::notifyStateChange(const std::string& state_name, bool old_value, bool new_value)
{
    if (old_value != new_value && state_change_callback_) {
        state_change_callback_(state_name, old_value, new_value);
    }
}

void StateManagerModule::handleRedLightTimer(double wheel_speed)
{
    ros::Time now = ros::Time::now();
    
    // 判断是否需要开始计时
    if ((state_flags_.is_block || state_flags_.located_flag || state_flags_.is_too_far || state_flags_.topic_stop) 
        && !state_flags_.time_count && state_flags_.control_mode && !state_flags_.arrived) {
        timer_state_.start_time = now;
        setTimeCount(true);
    }
    else if (wheel_speed > SPEED_THRESHOLD || !state_flags_.control_mode) {
        setTimeCount(false);
    }

    // 处理红灯警告标志
    if (state_flags_.time_count && timer_state_.start_time.isValid() && now >= timer_state_.start_time) {
        double elapsed = (now - timer_state_.start_time).toSec();
        if (elapsed > RED_LIGHT_TIMEOUT && elapsed < MAX_TIMER_DURATION) {
            setRedLightFlag(true);
        }
        else if (elapsed <= RED_LIGHT_TIMEOUT) {
            setRedLightFlag(false);
        }
    }
    else {
        setRedLightFlag(false);
    }
}

void StateManagerModule::handleHookupTimer(double wheel_speed)
{
    ros::Time now = ros::Time::now();
    
    // 判断是否需要开始自动挂钩计时
    if (state_flags_.control_mode && wheel_speed <= SPEED_THRESHOLD && 
        state_flags_.auto_hook && !state_flags_.time_count_hook) {
        timer_state_.start_time_hook = now;
        setTimeCountHook(true);
    }
    else if (wheel_speed > SPEED_THRESHOLD || !state_flags_.auto_hook) {
        setTimeCountHook(false);
    }

    // 处理挂钩标志
    if (state_flags_.time_count_hook && timer_state_.start_time_hook.isValid() && now >= timer_state_.start_time_hook) {
        double elapsed = (now - timer_state_.start_time_hook).toSec();
        if (elapsed > HOOKUP_TIMEOUT && elapsed < MAX_TIMER_DURATION) {
            setHookupFlag(true);
        }
        else if (elapsed <= HOOKUP_TIMEOUT) {
            setHookupFlag(false);
        }
    }
    else {
        setHookupFlag(false);
    }
}
