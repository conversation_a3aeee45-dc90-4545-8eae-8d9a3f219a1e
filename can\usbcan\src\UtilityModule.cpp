#include "UtilityModule.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <thread>

// MathUtils 实现
double MathUtils::calculateDistance(const geometry_msgs::Point& p1, const geometry_msgs::Point& p2)
{
    double dx = p2.x - p1.x;
    double dy = p2.y - p1.y;
    double dz = p2.z - p1.z;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

double MathUtils::angleDifference(double angle1, double angle2)
{
    double diff = angle2 - angle1;
    return normalizeAngle(diff);
}

double MathUtils::normalizeAngle(double angle)
{
    while (angle > M_PI) {
        angle -= 2.0 * M_PI;
    }
    while (angle < -M_PI) {
        angle += 2.0 * M_PI;
    }
    return angle;
}

double MathUtils::radiansToDegrees(double radians)
{
    return radians * 180.0 / M_PI;
}

double MathUtils::degreesToRadians(double degrees)
{
    return degrees * M_PI / 180.0;
}

double MathUtils::lerp(double a, double b, double t)
{
    return a + t * (b - a);
}

bool MathUtils::isApproximatelyEqual(double a, double b, double epsilon)
{
    return std::abs(a - b) < epsilon;
}

// TimeUtils 实现
uint64_t TimeUtils::getCurrentTimestampMs()
{
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
}

uint64_t TimeUtils::getCurrentTimestampUs()
{
    auto now = std::chrono::system_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

std::string TimeUtils::getCurrentTimeString(const std::string& format)
{
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), format.c_str());
    return ss.str();
}

double TimeUtils::getTimeDifferenceSeconds(const ros::Time& start, const ros::Time& end)
{
    return (end - start).toSec();
}

bool TimeUtils::isTimeout(const ros::Time& start_time, double timeout_seconds)
{
    ros::Time current_time = ros::Time::now();
    return getTimeDifferenceSeconds(start_time, current_time) > timeout_seconds;
}

void TimeUtils::sleepMs(int milliseconds)
{
    std::this_thread::sleep_for(std::chrono::milliseconds(milliseconds));
}

// FileUtils 实现
bool FileUtils::fileExists(const std::string& file_path)
{
    return std::filesystem::exists(file_path) && std::filesystem::is_regular_file(file_path);
}

bool FileUtils::directoryExists(const std::string& dir_path)
{
    return std::filesystem::exists(dir_path) && std::filesystem::is_directory(dir_path);
}

bool FileUtils::createDirectory(const std::string& dir_path)
{
    try {
        return std::filesystem::create_directories(dir_path);
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to create directory %s: %s", dir_path.c_str(), e.what());
        return false;
    }
}

long FileUtils::getFileSize(const std::string& file_path)
{
    try {
        if (fileExists(file_path)) {
            return static_cast<long>(std::filesystem::file_size(file_path));
        }
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to get file size for %s: %s", file_path.c_str(), e.what());
    }
    return -1;
}

std::string FileUtils::readFileToString(const std::string& file_path)
{
    try {
        std::ifstream file(file_path);
        if (!file.is_open()) {
            return "";
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to read file %s: %s", file_path.c_str(), e.what());
        return "";
    }
}

bool FileUtils::writeStringToFile(const std::string& file_path, const std::string& content, bool append)
{
    try {
        std::ios_base::openmode mode = std::ios::out;
        if (append) {
            mode |= std::ios::app;
        }
        
        std::ofstream file(file_path, mode);
        if (!file.is_open()) {
            return false;
        }
        
        file << content;
        return true;
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to write file %s: %s", file_path.c_str(), e.what());
        return false;
    }
}

std::string FileUtils::getFileExtension(const std::string& file_path)
{
    std::filesystem::path path(file_path);
    std::string extension = path.extension().string();
    if (!extension.empty() && extension[0] == '.') {
        extension = extension.substr(1); // 移除点号
    }
    return extension;
}

std::string FileUtils::getFileName(const std::string& file_path)
{
    std::filesystem::path path(file_path);
    return path.filename().string();
}

std::string FileUtils::getDirectoryPath(const std::string& file_path)
{
    std::filesystem::path path(file_path);
    return path.parent_path().string();
}

// StringUtils 实现
std::string StringUtils::trim(const std::string& str)
{
    size_t start = str.find_first_not_of(" \t\n\r\f\v");
    if (start == std::string::npos) {
        return "";
    }
    
    size_t end = str.find_last_not_of(" \t\n\r\f\v");
    return str.substr(start, end - start + 1);
}

std::string StringUtils::toLowerCase(const std::string& str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string StringUtils::toUpperCase(const std::string& str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter)
{
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = str.find(delimiter);
    
    while (end != std::string::npos) {
        tokens.push_back(str.substr(start, end - start));
        start = end + delimiter.length();
        end = str.find(delimiter, start);
    }
    
    tokens.push_back(str.substr(start));
    return tokens;
}

std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter)
{
    if (strings.empty()) {
        return "";
    }
    
    std::stringstream ss;
    for (size_t i = 0; i < strings.size(); ++i) {
        if (i > 0) {
            ss << delimiter;
        }
        ss << strings[i];
    }
    
    return ss.str();
}

bool StringUtils::startsWith(const std::string& str, const std::string& prefix)
{
    return str.length() >= prefix.length() && 
           str.compare(0, prefix.length(), prefix) == 0;
}

bool StringUtils::endsWith(const std::string& str, const std::string& suffix)
{
    return str.length() >= suffix.length() && 
           str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

std::string StringUtils::replaceAll(const std::string& str, const std::string& from, const std::string& to)
{
    std::string result = str;
    size_t start_pos = 0;
    
    while ((start_pos = result.find(from, start_pos)) != std::string::npos) {
        result.replace(start_pos, from.length(), to);
        start_pos += to.length();
    }
    
    return result;
}

// VehicleUtils 实现
double VehicleUtils::mpsToKmh(double mps)
{
    return mps * 3.6;
}

double VehicleUtils::kmhToMps(double kmh)
{
    return kmh / 3.6;
}

double VehicleUtils::wheelRpmToLinearVelocity(double wheel_rpm, double wheel_diameter_mm)
{
    // 转换公式：线速度 = 转速 * 圆周长 / 60
    double wheel_circumference_m = (wheel_diameter_mm / 1000.0) * M_PI;
    return (wheel_rpm * wheel_circumference_m) / 60.0;
}

double VehicleUtils::linearVelocityToWheelRpm(double linear_velocity, double wheel_diameter_mm)
{
    // 转换公式：转速 = 线速度 * 60 / 圆周长
    double wheel_circumference_m = (wheel_diameter_mm / 1000.0) * M_PI;
    return (linear_velocity * 60.0) / wheel_circumference_m;
}

void VehicleUtils::calculateAckermannAngles(double front_wheel_angle, double wheelbase, double track_width,
                                           double& inner_angle, double& outer_angle)
{
    if (std::abs(front_wheel_angle) < 1e-6) {
        // 直行时，内外轮转角相同
        inner_angle = outer_angle = front_wheel_angle;
        return;
    }

    // 计算转弯半径
    double turn_radius = wheelbase / std::tan(std::abs(front_wheel_angle));

    if (front_wheel_angle > 0) {
        // 左转
        inner_angle = std::atan(wheelbase / (turn_radius - track_width / 2.0));
        outer_angle = std::atan(wheelbase / (turn_radius + track_width / 2.0));
    } else {
        // 右转
        inner_angle = -std::atan(wheelbase / (turn_radius - track_width / 2.0));
        outer_angle = -std::atan(wheelbase / (turn_radius + track_width / 2.0));
    }
}
