#include "VehicleControlModule.h"
#include <iostream>
#include <iomanip>

VehicleControlModule::VehicleControlModule(djyx::Logger& logger)
    : logger_(logger)
    , last_velocity_(0.0f)
    , last_stop_log_time_(0.0)
{
    ROS_INFO("Vehicle Control Module initialized");
}

VehicleControlParams VehicleControlModule::processControlCommand(
    const auto_msgs::ControlCommandStampedConstPtr& msg,
    const VehicleState& vehicle_state)
{
    current_state_ = vehicle_state;
    
    // 计算方向盘角度
    double steering_angle = calculateSteeringAngle(msg->cmd.steering_angle);
    current_params_.left_angle = steering_angle >= 0 ? steering_angle : 0;
    current_params_.right_angle = steering_angle < 0 ? -steering_angle : 0;

    float velocity = msg->cmd.linear_velocity * 36; // 转换为 km/h

    // 检查是否需要停车
    if (shouldStop(vehicle_state) || velocity * last_velocity_ < 0 || velocity == 0) {
        return processBrakeLogic(velocity, vehicle_state);
    }
    else if (velocity > 0) {
        return processForwardLogic(velocity);
    }
    else if (velocity < 0) {
        return processReverseLogic(velocity);
    }

    last_velocity_ = velocity;
    return current_params_;
}

void VehicleControlModule::updateVehicleState(const VehicleState& state)
{
    current_state_ = state;
}

VehicleControlParams VehicleControlModule::getCurrentControlParams() const
{
    return current_params_;
}

void VehicleControlModule::resetPIDController()
{
    pid_params_.reset();
}

void VehicleControlModule::setPIDParams(const PIDParams& params)
{
    pid_params_ = params;
}

PIDParams VehicleControlModule::getPIDParams() const
{
    return pid_params_;
}

void VehicleControlModule::forceStop(const std::string& reason)
{
    current_params_.forward_speed = 0.0;
    current_params_.retreat_speed = calculateBrakeForce(current_state_.wheel_speed);
    current_params_.gear = 0x22; // N档
    current_params_.is_braking = true;
    
    logStopReason(reason);
}

bool VehicleControlModule::shouldStop(const VehicleState& vehicle_state) const
{
    return vehicle_state.is_block || 
           !vehicle_state.is_located || 
           vehicle_state.is_hooking || 
           vehicle_state.is_stopped || 
           vehicle_state.is_mission_stopped || 
           vehicle_state.is_too_far || 
           vehicle_state.is_safety_triggered || 
           vehicle_state.is_obstacle_detected ||
           vehicle_state.is_topic_stopped;
}

std::string VehicleControlModule::getStopReason(const VehicleState& vehicle_state) const
{
    if (vehicle_state.is_obstacle_detected)
        return "a*道路阻塞";
    else if (vehicle_state.is_block)
        return "op道路阻塞";
    else if (!vehicle_state.is_located)
        return "定位丢失";
    else if (vehicle_state.is_mission_stopped)
        return "任务暂停";
    else if (vehicle_state.is_too_far)
        return "偏离路径";
    else if (vehicle_state.is_topic_stopped)
        return "话题停止";
    else if (vehicle_state.is_hooking)
        return "挂钩中";
    else if (vehicle_state.is_stopped)
        return "已停车";
    else if (vehicle_state.is_safety_triggered)
        return "安全框触发";
    else
        return "未知原因";
}

double VehicleControlModule::calculateBrakeForce(double current_speed, double target_speed)
{
    if (current_speed == 0) {
        return 0.0;
    }

    // PID控制计算刹车力度
    double error = target_speed - current_speed;
    pid_params_.integral += error;
    double derivative = error - pid_params_.previous_error;
    
    double brake_force = 250 + pid_params_.kp * error + 
                        pid_params_.ki * pid_params_.integral + 
                        pid_params_.kd * derivative;

    // 限制刹车力度范围
    brake_force = std::clamp(brake_force, 250.0, 1000.0);
    
    pid_params_.previous_error = error;
    return brake_force;
}

double VehicleControlModule::calculateSteeringAngle(double steering_angle)
{
    // 将弧度转换为控制角度
    return std::clamp(steering_angle * (180.0 / M_PI) / 31.0 * 1022.0, -1200.0, 1200.0);
}

VehicleControlParams VehicleControlModule::processBrakeLogic(float velocity, const VehicleState& vehicle_state)
{
    VehicleControlParams params;
    
    // 记录停车原因（每5秒记录一次）
    double current_time = ros::Time::now().toSec();
    if (current_time - last_stop_log_time_ > 5.0) {
        std::string stop_reason = getStopReason(vehicle_state);
        logStopReason(stop_reason);
        last_stop_log_time_ = current_time;
    }

    // 记录详细错误日志
    if (vehicle_state.is_block) {
        logger_.logError("lane is block !");
    }
    else if (!vehicle_state.is_located) {
        logger_.logError("location lost !");
    }
    else if (vehicle_state.is_mission_stopped) {
        logger_.logError("mission stop !");
    }
    else if (vehicle_state.is_too_far) {
        logger_.logError("Far away from lane !");
    }

    params.forward_speed = 0.0;
    params.is_braking = true;

    if (current_state_.wheel_speed == 0) {
        params.retreat_speed = 0.0;
    } else {
        params.retreat_speed = calculateBrakeForce(current_state_.wheel_speed);
    }

    return params;
}

VehicleControlParams VehicleControlModule::processForwardLogic(float velocity)
{
    VehicleControlParams params;
    
    params.gear = 0x21; // D档
    params.forward_speed = velocity;
    params.retreat_speed = 0.0;
    params.is_braking = false;
    
    // 重置PID控制器
    pid_params_.reset();
    
    return params;
}

VehicleControlParams VehicleControlModule::processReverseLogic(float velocity)
{
    VehicleControlParams params;
    
    params.gear = 0x24; // R档
    params.forward_speed = -velocity; // 后退速度为正值
    params.retreat_speed = 0.0;
    params.is_braking = false;
    
    return params;
}

void VehicleControlModule::logStopReason(const std::string& reason)
{
    if (!reason.empty()) {
        std::cout << "[停车] 原因: " << reason << std::endl;
        ROS_WARN("Vehicle stopped due to: %s", reason.c_str());
    }
}
