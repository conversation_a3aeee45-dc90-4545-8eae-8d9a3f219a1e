#include "VehicleController.h"
#include <nlohmann/json.hpp>
#include <ros/package.h>
#include <fstream>

VehicleController::VehicleController(ros::NodeHandle& nh, djyx::Logger& logger)
    : nh_(nh)
    , logger_(logger)
    , reverse_module_(logger)
    , wheel_speed_(0.0)
    , forward_speed_(0.0)
    , retreat_speed_(0.0)
    , left_angle_(0.0)
    , right_angle_(0.0)
{
    ROS_INFO("VehicleController constructor called");
}

VehicleController::~VehicleController()
{
    stop();
    ROS_INFO("VehicleController destructor called");
}

bool VehicleController::initialize()
{
    try {
        // 初始化ROS接口
        initializeRosInterface();

        // 初始化功能模块
        if (!initializeModules()) {
            ROS_ERROR("Failed to initialize modules");
            return false;
        }

        // 设置模块间的回调函数
        setupModuleCallbacks();

        // 读取目标点配置
        goals_ = readJsonFile();
        ROS_INFO("Loaded %zu goals from JSON file", goals_.size());
        
        for (const auto& goal : goals_) {
            goal_map_[goal.name] = goal;
            ROS_INFO("Added goal: %s (%.2f, %.2f)", 
                     goal.name.c_str(), goal.position_x, goal.position_y);
        }

        // 创建定时器
        timer_ = nh_.createTimer(ros::Duration(0.1), 
                                &VehicleController::eventTimer, this);

        ROS_INFO("VehicleController initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to initialize VehicleController: %s", e.what());
        return false;
    }
}

void VehicleController::start()
{
    if (!can_comm_module_->initialize()) {
        ROS_ERROR("Failed to initialize CAN communication module");
        return;
    }

    ROS_INFO("VehicleController started");
}

void VehicleController::stop()
{
    if (timer_.isValid()) {
        timer_.stop();
    }

    if (can_comm_module_) {
        can_comm_module_->shutdown();
    }

    ROS_INFO("VehicleController stopped");
}

void VehicleController::processLoop()
{
    if (!can_comm_module_) {
        return;
    }

    // 接收CAN消息
    std::vector<VCI_CAN_OBJ> received_messages;
    if (can_comm_module_->receiveMessages(received_messages)) {
        for (const auto& can_obj : received_messages) {
            handleCanMessage(can_obj);
        }
    }

    // 发送控制消息
    sendCanControlMessage();
}

void VehicleController::initializeRosInterface()
{
    // 发布者
    wheels_pub_ = nh_.advertise<usbcan_msgs::WheelsEncoder>("/wheels", 10);
    speed_pub_ = nh_.advertise<std_msgs::Float32>("/speed", 10);
    goal_pub_ = nh_.advertise<geometry_msgs::PoseStamped>("/move_base_simple/goal", 1);
    soc_pub_ = nh_.advertise<std_msgs::UInt8>("/bsm_soc", 10);
    error_info_pub_ = nh_.advertise<std_msgs::UInt8MultiArray>("/error_info", 10);
    reverse_status_pub_ = nh_.advertise<std_msgs::Bool>("/reverse_status", 10);
    steering_angle_pub_ = nh_.advertise<usbcan_msgs::steering_angle>("/steering_angle", 10);

    // 订阅者
    ctrl_cmd_sub_ = nh_.subscribe("/ctrl_cmd", 10, 
                                 &VehicleController::ctrlCmdCallback, this);
    mission_state_sub_ = nh_.subscribe("/mission_state", 10, 
                                      &VehicleController::missionStateCallback, this);
    topic_state_sub_ = nh_.subscribe("/topic_state", 10, 
                                    &VehicleController::topicStateCallback, this);
    current_pose_sub_ = nh_.subscribe("/current_pose", 10, 
                                     &VehicleController::currentPoseCallback, this);

    // 其他订阅者
    nh_.subscribe("/isblock", 10, &VehicleController::isBlockCallback, this);
    nh_.subscribe("/is_too_far", 10, &VehicleController::isTooFarCallback, this);
    nh_.subscribe("/obstacle_detected", 10, &VehicleController::safetyBoxCallback, this);
    nh_.subscribe("/obstacle_waypoint", 10, &VehicleController::obstacleWaypointCallback, this);
    nh_.subscribe("/force_reverse_request", 10, &VehicleController::forceReverseCallback, this);

    ROS_INFO("ROS interface initialized");
}

bool VehicleController::initializeModules()
{
    try {
        // 初始化CAN通信模块
        can_comm_module_ = std::make_unique<CanCommunicationModule>(logger_);
        
        // 初始化消息处理模块
        message_processor_ = std::make_unique<MessageProcessorModule>(logger_);
        
        // 初始化车辆控制模块
        vehicle_control_ = std::make_unique<VehicleControlModule>(logger_);
        
        // 初始化状态管理模块
        state_manager_ = std::make_unique<StateManagerModule>(logger_);
        
        // 初始化日志记录模块
        logging_module_ = std::make_unique<LoggingModule>();
        if (!logging_module_->initialize()) {
            ROS_WARN("Failed to initialize logging module, continuing without logging");
        }

        ROS_INFO("All modules initialized successfully");
        return true;
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to initialize modules: %s", e.what());
        return false;
    }
}

void VehicleController::setupModuleCallbacks()
{
    // 设置消息处理模块的回调函数
    message_processor_->setButtonMessageCallback(
        [this](const ButtonMessageData& data) { onButtonMessage(data); });
    
    message_processor_->setButtonMessage2Callback(
        [this](const ButtonMessageData& data) { onButtonMessage2(data); });
    
    message_processor_->setSpeedMessageCallback(
        [this](const SpeedMessageData& data) { onSpeedMessage(data); });
    
    message_processor_->setBmsMessageCallback(
        [this](const BmsMessageData& data) { onBmsMessage(data); });
    
    message_processor_->setVehicleControlModeCallback(
        [this](const VehicleControlModeData& data) { onVehicleControlModeMessage(data); });
    
    message_processor_->setBodyFaultMessageCallback(
        [this](const VCI_CAN_OBJ& can_obj) { onBodyFaultMessage(can_obj); });

    // 设置状态管理模块的回调函数
    state_manager_->setStateChangeCallback(
        [this](const std::string& state_name, bool old_value, bool new_value) {
            onStateChange(state_name, old_value, new_value);
        });

    ROS_INFO("Module callbacks set up successfully");
}

std::vector<Goal> VehicleController::readJsonFile()
{
    std::vector<Goal> goals;
    
    try {
        std::string package_path = ros::package::getPath("ins");
        std::string filepath = package_path + "/config/line.json";
        
        std::ifstream file(filepath);
        if (!file.is_open()) {
            throw std::runtime_error("Cannot open file: " + filepath);
        }

        nlohmann::json j;
        file >> j;
        goals.reserve(j.size());

        for (const auto& item : j) {
            Goal goal;
            goal.name = item["name"];
            goal.position_x = item["position_x"];
            goal.position_y = item["position_y"];
            goal.position_z = item["position_z"];
            goal.orientation_x = item["orientation_x"];
            goal.orientation_y = item["orientation_y"];
            goal.orientation_z = item["orientation_z"];
            goal.orientation_w = item["orientation_w"];
            
            // 解析倒车标志和方向
            goal.reverse_flag = (item.value("need_reverse", "no") == "yes");
            goal.reverse_direction = item.value("reverse_direction", "left");
            
            goals.push_back(goal);
        }
    }
    catch (const std::exception& e) {
        ROS_ERROR("Failed to read goals from JSON file: %s", e.what());
    }
    
    return goals;
}

void VehicleController::eventTimer(const ros::TimerEvent& event)
{
    // 处理循环
    processLoop();
    
    // 更新状态管理器的定时器事件
    if (state_manager_) {
        state_manager_->handleTimerEvent(wheel_speed_);
    }
    
    // 发布车辆状态信息
    publishVehicleStatus();
}

void VehicleController::sendCanControlMessage()
{
    if (!can_comm_module_ || !vehicle_control_) {
        return;
    }

    // 获取当前的控制参数
    auto control_params = vehicle_control_->getCurrentControlParams();

    // 构造并发送车轮控制消息
    VCI_CAN_OBJ can_msg;
    can_msg.ID = 0x123; // 车轮控制消息ID
    can_msg.DataLen = 8;
    can_msg.SendType = 0;
    can_msg.RemoteFlag = 0;
    can_msg.ExternFlag = 0;

    // 根据控制参数填充数据
    // 这里需要根据具体的CAN协议来实现
    memset(can_msg.Data, 0, 8);

    can_comm_module_->sendMessage(can_msg);
}

// ROS回调函数实现
void VehicleController::ctrlCmdCallback(const auto_msgs::ControlCommandStampedConstPtr& msg)
{
    if (!vehicle_control_) {
        return;
    }

    // 将ROS控制命令转换为车辆控制参数
    VehicleControlParams params;
    params.target_speed = msg->cmd.linear_velocity;
    params.target_steering_angle = msg->cmd.steering_angle;

    vehicle_control_->updateControlCommand(params);
}

void VehicleController::missionStateCallback(const auto_msgs::MissionState::ConstPtr& msg)
{
    if (!state_manager_) {
        return;
    }

    // 更新任务状态
    state_manager_->updateMissionState(msg->state);
}

void VehicleController::topicStateCallback(const auto_msgs::TopicState::ConstPtr& msg)
{
    if (!state_manager_) {
        return;
    }

    // 更新话题状态
    state_manager_->updateTopicState(msg->topic_name, msg->is_active);
}

void VehicleController::currentPoseCallback(const geometry_msgs::PoseStamped::ConstPtr& msg)
{
    current_pose_ = *msg;

    if (vehicle_control_) {
        vehicle_control_->updateCurrentPose(*msg);
    }
}

void VehicleController::isBlockCallback(const std_msgs::Bool::ConstPtr& msg)
{
    if (state_manager_) {
        state_manager_->updateState("isblock", msg->data);
    }
}

void VehicleController::isTooFarCallback(const std_msgs::Bool::ConstPtr& msg)
{
    if (state_manager_) {
        state_manager_->updateState("isTooFar", msg->data);
    }
}

void VehicleController::safetyBoxCallback(const std_msgs::Bool::ConstPtr& msg)
{
    if (state_manager_) {
        state_manager_->updateState("Safety", msg->data);
    }
}

void VehicleController::obstacleWaypointCallback(const std_msgs::Bool::ConstPtr& msg)
{
    if (state_manager_) {
        state_manager_->updateState("obstacle_detected", msg->data);
    }
}

void VehicleController::forceReverseCallback(const std_msgs::Bool::ConstPtr& msg)
{
    if (msg->data && vehicle_control_) {
        // 触发强制倒车
        vehicle_control_->triggerEmergencyReverse();
    }
}

// 消息处理回调函数实现
void VehicleController::onButtonMessage(const ButtonMessageData& data)
{
    ROS_INFO("Button message received: button=%d, state=%d", data.button_id, data.state);

    // 根据按钮消息更新状态
    if (state_manager_) {
        std::string button_state = "button_" + std::to_string(data.button_id);
        state_manager_->updateState(button_state, data.state != 0);
    }
}

void VehicleController::onButtonMessage2(const ButtonMessageData& data)
{
    ROS_INFO("Button2 message received: button=%d, state=%d", data.button_id, data.state);

    // 处理第二组按钮消息
    if (state_manager_) {
        std::string button_state = "button2_" + std::to_string(data.button_id);
        state_manager_->updateState(button_state, data.state != 0);
    }
}

void VehicleController::onSpeedMessage(const SpeedMessageData& data)
{
    wheel_speed_ = data.speed;

    // 发布速度信息
    std_msgs::Float32 speed_msg;
    speed_msg.data = wheel_speed_;
    speed_pub_.publish(speed_msg);

    // 发布车轮编码器信息
    usbcan_msgs::WheelsEncoder wheels_msg;
    wheels_msg.left_wheel = data.left_wheel_speed;
    wheels_msg.right_wheel = data.right_wheel_speed;
    wheels_pub_.publish(wheels_msg);
}

void VehicleController::onBmsMessage(const BmsMessageData& data)
{
    // 发布电池SOC信息
    std_msgs::UInt8 soc_msg;
    soc_msg.data = data.soc;
    soc_pub_.publish(soc_msg);

    // 更新电池状态
    if (state_manager_) {
        state_manager_->updateState("is_BatteryLow", data.soc < 20); // 20%以下认为电量低
    }
}

void VehicleController::onVehicleControlModeMessage(const VehicleControlModeData& data)
{
    ROS_INFO("Vehicle control mode: %d", data.control_mode);

    if (state_manager_) {
        state_manager_->updateState("ControlMode", data.control_mode == 1);
    }
}

void VehicleController::onBodyFaultMessage(const VCI_CAN_OBJ& can_obj)
{
    ROS_WARN("Body fault message received: ID=0x%X", can_obj.ID);

    // 发布错误信息
    error_info_.data.clear();
    error_info_.data.resize(10, 0);

    // 根据故障数据填充错误信息
    for (int i = 0; i < can_obj.DataLen && i < 8; ++i) {
        if (i < error_info_.data.size()) {
            error_info_.data[i] = can_obj.Data[i];
        }
    }

    error_info_pub_.publish(error_info_);
}

// 状态变化回调函数实现
void VehicleController::onStateChange(const std::string& state_name, bool old_value, bool new_value)
{
    ROS_INFO("State changed: %s from %s to %s",
             state_name.c_str(),
             old_value ? "true" : "false",
             new_value ? "true" : "false");

    // 根据状态变化执行相应的动作
    if (state_name == "Mission_Stop" && new_value) {
        // 任务停止，停止车辆
        if (vehicle_control_) {
            vehicle_control_->emergencyStop();
        }
    }
    else if (state_name == "Safety" && new_value) {
        // 安全状态激活，减速或停车
        if (vehicle_control_) {
            vehicle_control_->activateSafetyMode();
        }
    }
    else if (state_name == "is_BatteryLow" && new_value) {
        // 电池电量低，发出警告
        ROS_WARN("Battery level is low!");
    }
}

void VehicleController::publishVehicleStatus()
{
    // 发布倒车状态
    std_msgs::Bool reverse_status;
    if (vehicle_control_) {
        reverse_status.data = vehicle_control_->isInReverseMode();
        reverse_status_pub_.publish(reverse_status);
    }

    // 发布转向角度
    if (vehicle_control_) {
        usbcan_msgs::steering_angle steering_msg;
        steering_msg.angle = vehicle_control_->getCurrentSteeringAngle();
        steering_angle_pub_.publish(steering_msg);
    }
}

void VehicleController::handleCanMessage(const VCI_CAN_OBJ& can_obj)
{
    if (!message_processor_) {
        return;
    }

    // 记录CAN数据到日志
    if (logging_module_) {
        logging_module_->logCanMessage(can_obj);
    }

    // 处理CAN消息
    message_processor_->processMessage(can_obj);
}
