#include "VehicleController.h"
#include "VelocityToPulse.h"
#include "djyxlogger.h"
#include <ros/ros.h>
#include <thread>
#include <chrono>

int main(int argc, char **argv)
{
    ros::init(argc, argv, "usbcan");
    ros::NodeHandle nh;

    djyx::Logger logger(200, 15); // 日志保存间隔（最大数量，最大天数）

    try {
        // 创建并初始化VehicleController
        VehicleController vc(nh, logger);

        if (!vc.initialize()) {
            ROS_ERROR("Failed to initialize VehicleController");
            return -1;
        }

        ROS_INFO("VehicleController initialized successfully");

        // 启动控制器
        vc.start();

        // 创建线程执行ProcessLoop
        std::thread processLoopThread([&vc]() {
            ros::Rate rate(10); // 10Hz
            while (ros::ok()) {
                vc.processLoop();
                rate.sleep();
            }
        });

        ROS_INFO("Vehicle control system started");

        // 使用ros::spin()来处理回调
        ros::spin();

        // 停止控制器
        vc.stop();

        // 等待线程结束
        if (processLoopThread.joinable()) {
            processLoopThread.join();
        }

        ROS_INFO("Vehicle control system stopped");
    }
    catch (const std::exception& e) {
        ROS_ERROR("Exception in main: %s", e.what());
        logger.saveErrorLog("error_log.txt");
        return -1;
    }

    // 保存错误日志
    logger.saveErrorLog("error_log.txt");

    return 0;
}