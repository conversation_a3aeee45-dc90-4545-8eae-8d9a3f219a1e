/**
 * @file test_modularization.cpp
 * @brief 测试模块化重构结果的简单测试程序
 */

#include <ros/ros.h>
#include <iostream>
#include <memory>

// 包含所有模块头文件
#include "CanCommunicationModule.h"
#include "MessageProcessorModule.h"
#include "VehicleControlModule.h"
#include "StateManagerModule.h"
#include "LoggingModule.h"
#include "UtilityModule.h"
#include "VehicleController.h"
#include "djyxlogger.h"

/**
 * @brief 测试各个模块的基本功能
 */
class ModularizationTester
{
public:
    ModularizationTester(ros::NodeHandle& nh, djyx::Logger& logger)
        : nh_(nh), logger_(logger)
    {
    }

    /**
     * @brief 测试所有模块的创建和初始化
     */
    bool testModuleCreation()
    {
        std::cout << "=== 测试模块创建 ===" << std::endl;
        
        try {
            // 测试CAN通信模块
            auto can_module = std::make_unique<CanCommunicationModule>(logger_);
            std::cout << "✓ CAN通信模块创建成功" << std::endl;
            
            // 测试消息处理模块
            auto message_module = std::make_unique<MessageProcessorModule>(logger_);
            std::cout << "✓ 消息处理模块创建成功" << std::endl;
            
            // 测试车辆控制模块
            auto control_module = std::make_unique<VehicleControlModule>(logger_);
            std::cout << "✓ 车辆控制模块创建成功" << std::endl;
            
            // 测试状态管理模块
            auto state_module = std::make_unique<StateManagerModule>(logger_);
            std::cout << "✓ 状态管理模块创建成功" << std::endl;
            
            // 测试日志记录模块
            auto logging_module = std::make_unique<LoggingModule>();
            std::cout << "✓ 日志记录模块创建成功" << std::endl;
            
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "✗ 模块创建失败: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief 测试工具模块的功能
     */
    bool testUtilityModule()
    {
        std::cout << "\n=== 测试工具模块 ===" << std::endl;
        
        try {
            // 测试数学工具
            double distance = MathUtils::calculateDistance(0, 0, 3, 4);
            if (std::abs(distance - 5.0) < 0.001) {
                std::cout << "✓ 数学工具距离计算正确: " << distance << std::endl;
            } else {
                std::cout << "✗ 数学工具距离计算错误: " << distance << std::endl;
                return false;
            }
            
            // 测试Ackermann转向计算
            auto steering = MathUtils::calculateAckermannSteering(2.5, 30.0);
            std::cout << "✓ Ackermann转向计算: 内轮=" << steering.inner_angle 
                      << "°, 外轮=" << steering.outer_angle << "°" << std::endl;
            
            // 测试文件工具
            if (FileUtils::directoryExists(".")) {
                std::cout << "✓ 文件工具目录检查正常" << std::endl;
            } else {
                std::cout << "✗ 文件工具目录检查失败" << std::endl;
                return false;
            }
            
            // 测试时间工具
            auto current_time = TimeUtils::getCurrentTimeString();
            std::cout << "✓ 时间工具获取当前时间: " << current_time << std::endl;
            
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "✗ 工具模块测试失败: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief 测试VehicleController的创建和初始化
     */
    bool testVehicleController()
    {
        std::cout << "\n=== 测试VehicleController ===" << std::endl;
        
        try {
            // 创建VehicleController
            auto controller = std::make_unique<VehicleController>(nh_, logger_);
            std::cout << "✓ VehicleController创建成功" << std::endl;
            
            // 测试初始化
            if (controller->initialize()) {
                std::cout << "✓ VehicleController初始化成功" << std::endl;
            } else {
                std::cout << "✗ VehicleController初始化失败" << std::endl;
                return false;
            }
            
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "✗ VehicleController测试失败: " << e.what() << std::endl;
            return false;
        }
    }

    /**
     * @brief 运行所有测试
     */
    bool runAllTests()
    {
        std::cout << "开始模块化重构验证测试...\n" << std::endl;
        
        bool all_passed = true;
        
        // 测试模块创建
        if (!testModuleCreation()) {
            all_passed = false;
        }
        
        // 测试工具模块
        if (!testUtilityModule()) {
            all_passed = false;
        }
        
        // 测试VehicleController
        if (!testVehicleController()) {
            all_passed = false;
        }
        
        std::cout << "\n=== 测试结果 ===" << std::endl;
        if (all_passed) {
            std::cout << "🎉 所有测试通过！模块化重构成功！" << std::endl;
        } else {
            std::cout << "❌ 部分测试失败，需要检查模块实现" << std::endl;
        }
        
        return all_passed;
    }

private:
    ros::NodeHandle& nh_;
    djyx::Logger& logger_;
};

int main(int argc, char** argv)
{
    // 初始化ROS
    ros::init(argc, argv, "test_modularization");
    ros::NodeHandle nh;
    
    // 创建日志记录器
    djyx::Logger logger("test_modularization");
    
    // 创建测试器并运行测试
    ModularizationTester tester(nh, logger);
    bool success = tester.runAllTests();
    
    if (success) {
        std::cout << "\n模块化重构验证完成，所有功能正常！" << std::endl;
        return 0;
    } else {
        std::cout << "\n模块化重构验证发现问题，请检查实现！" << std::endl;
        return 1;
    }
}
